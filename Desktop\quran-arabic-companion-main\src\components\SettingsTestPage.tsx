import React, { useState } from 'react';
import { ArrowRight, Play, CheckCircle, XCircle, RotateCcw } from 'lucide-react';
import { useQuranSettings } from '../hooks/useQuranSettings';
import {
  testAllSettingsApplication,
  testFontSizeApplication,
  testThemeColorApplication,
  testTextAlignmentApplication,
  runAllIntegrationTests
} from '../tests/settings-integration.test';
import {
  runAllComprehensiveTests,
  testAllFontSizes,
  testAllThemeColors,
  testAllTextAlignments,
  testAllNumberFormats,
  testAllDisplayModes
} from '../tests/comprehensive-settings.test';
import {
  runAllResetTests,
  testSettingsReset,
  testPartialSettingsReset,
  testResetWithCorruptedData,
  testResetWithEmptyStorage
} from '../tests/settings-reset.test';
import {
  runAllCompatibilityTests,
  getBrowserInfo
} from '../tests/browser-compatibility.test';

interface SettingsTestPageProps {
  onBack: () => void;
  onGoToReader?: () => void;
}

interface TestResult {
  name: string;
  description: string;
  status: 'pending' | 'running' | 'passed' | 'failed';
  details?: string;
}

const SettingsTestPage: React.FC<SettingsTestPageProps> = ({ onBack, onGoToReader }) => {
  const { settings, resetSettings } = useQuranSettings();
  const [testResults, setTestResults] = useState<TestResult[]>([
    {
      name: 'localStorage Save',
      description: 'اختبار حفظ الإعدادات في localStorage',
      status: 'pending'
    },
    {
      name: 'localStorage Load',
      description: 'اختبار استرجاع الإعدادات من localStorage',
      status: 'pending'
    },
    {
      name: 'Settings Persistence',
      description: 'اختبار استمرارية الإعدادات بعد إعادة التحميل',
      status: 'pending'
    },
    {
      name: 'Default Values',
      description: 'اختبار القيم الافتراضية للإعدادات',
      status: 'pending'
    },
    {
      name: 'Settings Update',
      description: 'اختبار تحديث الإعدادات',
      status: 'pending'
    },
    {
      name: 'Settings Application',
      description: 'اختبار تطبيق الإعدادات على النص',
      status: 'pending'
    },
    {
      name: 'Theme Colors',
      description: 'اختبار ألوان الثيم المختلفة',
      status: 'pending'
    },
    {
      name: 'Quick Settings Modal',
      description: 'اختبار نافذة الإعدادات السريعة',
      status: 'pending'
    },
    {
      name: 'Settings Sync',
      description: 'اختبار مزامنة الإعدادات بين المكونات',
      status: 'pending'
    }
  ]);

  const updateTestResult = (index: number, status: TestResult['status'], details?: string) => {
    setTestResults(prev => prev.map((test, i) => 
      i === index ? { ...test, status, details } : test
    ));
  };

  const testLocalStorageSave = async (): Promise<boolean> => {
    try {
      const testKey = 'test-quran-settings';
      const testData = { fontSize: 24, colorTheme: 'blue' };
      
      localStorage.setItem(testKey, JSON.stringify(testData));
      const retrieved = localStorage.getItem(testKey);
      
      if (retrieved) {
        const parsed = JSON.parse(retrieved);
        localStorage.removeItem(testKey);
        return parsed.fontSize === 24 && parsed.colorTheme === 'blue';
      }
      
      return false;
    } catch (error) {
      return false;
    }
  };

  const testLocalStorageLoad = async (): Promise<boolean> => {
    try {
      const testKey = 'test-quran-settings-load';
      const testData = { fontSize: 28, colorTheme: 'red' };
      
      localStorage.setItem(testKey, JSON.stringify(testData));
      
      // محاكاة إعادة تحميل الصفحة
      const retrieved = localStorage.getItem(testKey);
      
      if (retrieved) {
        const parsed = JSON.parse(retrieved);
        localStorage.removeItem(testKey);
        return parsed.fontSize === 28 && parsed.colorTheme === 'red';
      }
      
      return false;
    } catch (error) {
      return false;
    }
  };

  const testSettingsPersistence = async (): Promise<boolean> => {
    try {
      const currentSettings = localStorage.getItem('quran-settings');
      return currentSettings !== null;
    } catch (error) {
      return false;
    }
  };

  const testDefaultValues = async (): Promise<boolean> => {
    try {
      // التحقق من القيم الافتراضية
      const expectedDefaults = {
        mushafType: 'connected',
        colorTheme: 'green',
        displayMode: 'dark',
        fontSize: 20,
        numberFormat: 'arabic',
        textAlignment: 'justified',
        showSurahNames: true,
        showJuzInfo: true
      };

      // مقارنة مع الإعدادات الحالية بعد إعادة التعيين
      resetSettings();
      
      // انتظار قصير للتأكد من تطبيق الإعدادات
      await new Promise(resolve => setTimeout(resolve, 100));
      
      return Object.keys(expectedDefaults).every(key => {
        const expected = expectedDefaults[key as keyof typeof expectedDefaults];
        const actual = settings[key as keyof typeof settings];
        return expected === actual;
      });
    } catch (error) {
      return false;
    }
  };

  const testSettingsUpdate = async (): Promise<boolean> => {
    try {
      // اختبار تحديث الإعدادات من خلال localStorage
      const currentSettings = localStorage.getItem('quran-settings');
      if (!currentSettings) return false;

      const parsed = JSON.parse(currentSettings);
      const originalFontSize = parsed.fontSize;

      // تحديث حجم النص
      const newFontSize = originalFontSize === 20 ? 24 : 20;
      const updatedSettings = { ...parsed, fontSize: newFontSize };

      localStorage.setItem('quran-settings', JSON.stringify(updatedSettings));

      // التحقق من التحديث
      const updatedData = localStorage.getItem('quran-settings');
      if (!updatedData) return false;

      const finalSettings = JSON.parse(updatedData);

      // إعادة الإعداد الأصلي
      localStorage.setItem('quran-settings', JSON.stringify(parsed));

      return finalSettings.fontSize === newFontSize;
    } catch (error) {
      return false;
    }
  };

  const testSettingsApplication = async (): Promise<boolean> => {
    try {
      // استخدام اختبارات التكامل الشاملة
      return testAllSettingsApplication(settings);
    } catch (error) {
      console.error('خطأ في اختبار تطبيق الإعدادات:', error);
      return false;
    }
  };

  const testThemeColors = async (): Promise<boolean> => {
    try {
      // اختبار ألوان الثيم المختلفة
      const themes = ['red', 'gray', 'blue', 'green', 'dark'];
      const themeColors = {
        red: '#ef4444',
        gray: '#6b7280',
        blue: '#3b82f6',
        green: '#22c55e',
        dark: '#1f2937'
      };

      // التحقق من وجود جميع الألوان
      for (const theme of themes) {
        if (!themeColors[theme as keyof typeof themeColors]) {
          return false;
        }
      }

      return true;
    } catch (error) {
      return false;
    }
  };

  const testQuickSettingsModal = async (): Promise<boolean> => {
    try {
      // اختبار وجود عناصر الإعدادات السريعة في DOM
      // هذا اختبار مبسط - في التطبيق الحقيقي نحتاج لمحاكاة فتح النافذة

      // التحقق من وجود localStorage للإعدادات
      const settingsData = localStorage.getItem('quran-settings');
      if (!settingsData) return false;

      const settings = JSON.parse(settingsData);

      // التحقق من وجود جميع الإعدادات المطلوبة للنافذة السريعة
      const requiredSettings = ['fontSize', 'colorTheme', 'displayMode'];

      for (const setting of requiredSettings) {
        if (!(setting in settings)) {
          return false;
        }
      }

      return true;
    } catch (error) {
      return false;
    }
  };

  const testSettingsSync = async (): Promise<boolean> => {
    try {
      // اختبار مزامنة الإعدادات بين المكونات
      const originalSettings = localStorage.getItem('quran-settings');

      // تغيير إعداد واحد
      const currentSettings = JSON.parse(originalSettings || '{}');
      const newFontSize = currentSettings.fontSize === 20 ? 24 : 20;

      const updatedSettings = {
        ...currentSettings,
        fontSize: newFontSize
      };

      localStorage.setItem('quran-settings', JSON.stringify(updatedSettings));

      // محاكاة إعادة تحميل الإعدادات
      await new Promise(resolve => setTimeout(resolve, 100));

      // التحقق من المزامنة
      const syncedSettings = localStorage.getItem('quran-settings');
      const parsedSynced = JSON.parse(syncedSettings || '{}');

      // إعادة الإعدادات الأصلية
      if (originalSettings) {
        localStorage.setItem('quran-settings', originalSettings);
      }

      return parsedSynced.fontSize === newFontSize;
    } catch (error) {
      return false;
    }
  };

  const runTest = async (index: number) => {
    updateTestResult(index, 'running');
    
    let result = false;
    let details = '';
    
    try {
      switch (index) {
        case 0:
          result = await testLocalStorageSave();
          details = result ? 'تم حفظ البيانات واسترجاعها بنجاح' : 'فشل في حفظ أو استرجاع البيانات';
          break;
        case 1:
          result = await testLocalStorageLoad();
          details = result ? 'تم تحميل البيانات بنجاح' : 'فشل في تحميل البيانات';
          break;
        case 2:
          result = await testSettingsPersistence();
          details = result ? 'الإعدادات محفوظة في localStorage' : 'لا توجد إعدادات محفوظة';
          break;
        case 3:
          result = await testDefaultValues();
          details = result ? 'جميع القيم الافتراضية صحيحة' : 'بعض القيم الافتراضية غير صحيحة';
          break;
        case 4:
          result = await testSettingsUpdate();
          details = result ? 'تم تحديث الإعدادات بنجاح' : 'فشل في تحديث الإعدادات';
          break;
        case 5:
          result = await testSettingsApplication();
          details = result ? 'تم تطبيق الإعدادات على النص بنجاح' : 'فشل في تطبيق الإعدادات';
          break;
        case 6:
          result = await testThemeColors();
          details = result ? 'جميع ألوان الثيم متوفرة' : 'بعض ألوان الثيم مفقودة';
          break;
        case 7:
          result = await testQuickSettingsModal();
          details = result ? 'نافذة الإعدادات السريعة جاهزة' : 'مشكلة في نافذة الإعدادات السريعة';
          break;
        case 8:
          result = await testSettingsSync();
          details = result ? 'مزامنة الإعدادات تعمل بشكل صحيح' : 'مشكلة في مزامنة الإعدادات';
          break;
      }
    } catch (error) {
      result = false;
      details = `خطأ: ${error}`;
    }
    
    updateTestResult(index, result ? 'passed' : 'failed', details);
  };

  const runAllTests = async () => {
    for (let i = 0; i < testResults.length; i++) {
      await runTest(i);
      // انتظار قصير بين الاختبارات
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'passed':
        return <CheckCircle className="text-green-400" size={20} />;
      case 'failed':
        return <XCircle className="text-red-400" size={20} />;
      case 'running':
        return <div className="w-5 h-5 border-2 border-blue-400 border-t-transparent rounded-full animate-spin" />;
      default:
        return <div className="w-5 h-5 border-2 border-gray-400 rounded-full" />;
    }
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'passed':
        return 'border-green-500 bg-green-500/10';
      case 'failed':
        return 'border-red-500 bg-red-500/10';
      case 'running':
        return 'border-blue-500 bg-blue-500/10';
      default:
        return 'border-gray-600 bg-gray-700/50';
    }
  };

  const passedTests = testResults.filter(test => test.status === 'passed').length;
  const failedTests = testResults.filter(test => test.status === 'failed').length;
  const totalTests = testResults.length;

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white">
      {/* Header */}
      <div className="bg-gray-900/80 backdrop-blur-sm border-b border-gray-700 p-4 sticky top-0 z-10">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Play className="text-blue-400" size={24} />
            <h1 className="text-xl font-bold">اختبار نظام الإعدادات</h1>
          </div>
          <button 
            onClick={onBack} 
            className="flex items-center gap-2 px-3 py-2 rounded-lg text-green-400 hover:text-green-300 hover:bg-green-400/10 transition-all duration-200"
          >
            <span className="text-sm">رجوع</span>
            <ArrowRight size={18} />
          </button>
        </div>
      </div>

      <div className="p-6 max-w-4xl mx-auto space-y-6">
        {/* Test Summary */}
        <div className="bg-gray-800/50 backdrop-blur-sm rounded-2xl p-6 border border-gray-700">
          <h2 className="text-lg font-semibold mb-4">ملخص الاختبارات</h2>
          <div className="grid grid-cols-3 gap-4 text-center">
            <div className="bg-green-500/20 rounded-lg p-3 border border-green-500">
              <div className="text-2xl font-bold text-green-400">{passedTests}</div>
              <div className="text-sm text-gray-300">نجح</div>
            </div>
            <div className="bg-red-500/20 rounded-lg p-3 border border-red-500">
              <div className="text-2xl font-bold text-red-400">{failedTests}</div>
              <div className="text-sm text-gray-300">فشل</div>
            </div>
            <div className="bg-blue-500/20 rounded-lg p-3 border border-blue-500">
              <div className="text-2xl font-bold text-blue-400">{totalTests}</div>
              <div className="text-sm text-gray-300">المجموع</div>
            </div>
          </div>
        </div>

        {/* Test Controls */}
        <div className="bg-gray-800/50 backdrop-blur-sm rounded-2xl p-6 border border-gray-700">
          <div className="flex gap-4 justify-center">
            <button
              onClick={runAllTests}
              className="flex items-center gap-2 px-6 py-3 bg-blue-600 hover:bg-blue-500 rounded-lg transition-all duration-200 font-medium"
            >
              <Play size={18} />
              تشغيل جميع الاختبارات
            </button>
            <button
              onClick={() => {
                console.log('🚀 بدء الاختبارات الشاملة...');
                const result = runAllComprehensiveTests();
                if (result) {
                  alert('✅ نجحت جميع الاختبارات الشاملة!');
                } else {
                  alert('❌ فشل في بعض الاختبارات الشاملة. راجع وحدة التحكم للتفاصيل.');
                }
              }}
              className="flex items-center gap-2 px-6 py-3 bg-purple-600 hover:bg-purple-500 rounded-lg transition-all duration-200 font-medium"
            >
              🔬 اختبارات شاملة
            </button>
            <button
              onClick={() => {
                console.log('🚀 بدء اختبارات إعادة التعيين...');
                const result = runAllResetTests();
                if (result) {
                  alert('✅ نجحت جميع اختبارات إعادة التعيين!');
                } else {
                  alert('❌ فشل في بعض اختبارات إعادة التعيين. راجع وحدة التحكم للتفاصيل.');
                }
              }}
              className="flex items-center gap-2 px-6 py-3 bg-orange-600 hover:bg-orange-500 rounded-lg transition-all duration-200 font-medium"
            >
              🔄 اختبار إعادة التعيين
            </button>
            <button
              onClick={() => {
                console.log('🚀 بدء اختبارات التوافق مع المتصفحات...');
                const browserInfo = getBrowserInfo();
                const result = runAllCompatibilityTests();
                if (result) {
                  alert(`✅ المتصفح متوافق!\n🌐 ${browserInfo.name} ${browserInfo.version}\n📱 ${browserInfo.mobile ? 'جهاز محمول' : 'جهاز مكتبي'}`);
                } else {
                  alert(`⚠️ مشاكل في التوافق مع المتصفح.\n🌐 ${browserInfo.name} ${browserInfo.version}\nراجع وحدة التحكم للتفاصيل.`);
                }
              }}
              className="flex items-center gap-2 px-6 py-3 bg-indigo-600 hover:bg-indigo-500 rounded-lg transition-all duration-200 font-medium"
            >
              🌐 اختبار التوافق
            </button>
            <button
              onClick={resetSettings}
              className="flex items-center gap-2 px-6 py-3 bg-gray-600 hover:bg-gray-500 rounded-lg transition-all duration-200 font-medium"
            >
              <RotateCcw size={18} />
              إعادة تعيين الإعدادات
            </button>
            {onGoToReader && (
              <button
                onClick={onGoToReader}
                className="flex items-center gap-2 px-6 py-3 bg-green-600 hover:bg-green-500 rounded-lg transition-all duration-200 font-medium"
              >
                📖 اختبار الإعدادات في القراءة
              </button>
            )}
          </div>
        </div>

        {/* Test Results */}
        <div className="bg-gray-800/50 backdrop-blur-sm rounded-2xl p-6 border border-gray-700">
          <h2 className="text-lg font-semibold mb-4">نتائج الاختبارات</h2>
          <div className="space-y-4">
            {testResults.map((test, index) => (
              <div
                key={index}
                className={`rounded-xl p-4 border transition-all duration-200 ${getStatusColor(test.status)}`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    {getStatusIcon(test.status)}
                    <div>
                      <div className="font-medium">{test.description}</div>
                      <div className="text-sm text-gray-400">{test.name}</div>
                      {test.details && (
                        <div className="text-xs text-gray-500 mt-1">{test.details}</div>
                      )}
                    </div>
                  </div>
                  <button
                    onClick={() => runTest(index)}
                    disabled={test.status === 'running'}
                    className="px-3 py-1 bg-gray-600 hover:bg-gray-500 rounded text-sm transition-colors disabled:opacity-50"
                  >
                    تشغيل
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Current Settings Display */}
        <div className="bg-gray-800/50 backdrop-blur-sm rounded-2xl p-6 border border-gray-700">
          <h2 className="text-lg font-semibold mb-4">الإعدادات الحالية</h2>
          <div className="grid grid-cols-2 gap-4 text-sm mb-6">
            <div>حجم النص: {settings.fontSize}px</div>
            <div>لون الثيم: {settings.colorTheme}</div>
            <div>وضع العرض: {settings.displayMode}</div>
            <div>نوع الأرقام: {settings.numberFormat}</div>
            <div>محاذاة النص: {settings.textAlignment}</div>
          </div>

          {/* Live Preview */}
          <div className="bg-gray-900/50 rounded-xl p-6 text-center border border-gray-600">
            <h3 className="text-sm text-gray-400 mb-3">معاينة مباشرة للإعدادات</h3>
            <div
              className="text-right leading-relaxed"
              style={{
                fontFamily: 'UthmanicHafs, Amiri, serif',
                fontSize: `${settings.fontSize}px`,
                textAlign: settings.textAlignment === 'justified' ? 'justify' : 'right',
                color: settings.colorTheme === 'red' ? '#ef4444' :
                       settings.colorTheme === 'blue' ? '#3b82f6' :
                       settings.colorTheme === 'green' ? '#22c55e' :
                       settings.colorTheme === 'gray' ? '#6b7280' : '#1f2937'
              }}
            >
              بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ
              <br />
              الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ
              <br />
              الرَّحْمَٰنِ الرَّحِيمِ
            </div>
            <div className="mt-4 text-xs text-gray-500">
              هذا النص يتغير حسب الإعدادات المطبقة
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SettingsTestPage;
