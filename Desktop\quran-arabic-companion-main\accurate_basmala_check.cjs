// سكربت دقيق لفحص وجود البسملة (أول آية) لكل قارئ
// يعتمد على HTTP status code وحجم الملف بدقة أكبر
// يفحص فقط 3 سور: الفاتحة، البقرة، آل عمران

const https = require('https');

// قائمة القراء المحدثة (حفص وورش) - بدون القراء غير الشغالين
const RECITERS = [
  // حفص عن عاصم
  { name: 'عبد الباسط عبد الصمد (مرتل)', folder: 'Abdul_Basit_Murattal_64kbps', riwaya: 'حفص' },
  { name: 'عبد الباسط عبد الصمد (مرتل - جودة عالية)', folder: 'Abdul_<PERSON><PERSON><PERSON>_Murattal_192kbps', riwaya: 'حفص' },
  { name: 'ع<PERSON><PERSON> الباسط عبد الصمد (مجود)', folder: '<PERSON>_<PERSON><PERSON><PERSON>_Mujawwad_128kbps', riwaya: 'حفص' },
  { name: 'محمود خليل الحصري', folder: 'Husary_64kbps', riwaya: 'حفص' },
  { name: 'محمود خليل الحصري', folder: 'Husary_128kbps', riwaya: 'حفص' },
  { name: 'محمود خليل الحصري (مجود)', folder: 'Husary_Mujawwad_64kbps', riwaya: 'حفص' },
  { name: 'محمود خليل الحصري (مجود)', folder: 'Husary_128kbps_Mujawwad', riwaya: 'حفص' },
  { name: 'محمود خليل الحصري (معلم)', folder: 'Husary_Muallim_128kbps', riwaya: 'حفص' },
  { name: 'محمد صديق المنشاوي (جودة منخفضة جداً)', folder: 'Menshawi_16kbps', riwaya: 'حفص' },
  { name: 'محمد صديق المنشاوي (جودة منخفضة)', folder: 'Menshawi_32kbps', riwaya: 'حفص' },
  { name: 'محمد صديق المنشاوي (مجود)', folder: 'Minshawy_Mujawwad_64kbps', riwaya: 'حفص' },
  { name: 'محمد صديق المنشاوي (مجود)', folder: 'Minshawy_Mujawwad_128kbps', riwaya: 'حفص' },
  { name: 'محمد صديق المنشاوي (معلم)', folder: 'Minshawy_Muallim_128kbps', riwaya: 'حفص' },
  { name: 'محمد صديق المنشاوي (معلم)', folder: 'Minshawy_Muallim_64kbps', riwaya: 'حفص' },
  { name: 'إبراهيم الأخضر', folder: 'Ibrahim_Akhdar_32kbps', riwaya: 'حفص' },
  { name: 'إبراهيم الأخضر', folder: 'Ibrahim_Akhdar_64kbps', riwaya: 'حفص' },
  { name: 'عبد الرحمن السديس', folder: 'Abdurrahmaan_As-Sudais_64kbps', riwaya: 'حفص' },
  { name: 'سعود الشريم', folder: 'Saood_ash-Shuraym_64kbps', riwaya: 'حفص' },
  { name: 'ماهر المعيقلي', folder: 'Maher_AlMuaiqly_64kbps', riwaya: 'حفص' },
  { name: 'نبيل الرفاعي', folder: 'Nabil_Rifa3i_48kbps', riwaya: 'حفص' },
  { name: 'نبيل الرفاعي', folder: 'Nabil_Rifa3i_64kbps', riwaya: 'حفص' },
  
  // ورش عن نافع
  { name: 'عبد الباسط عبد الصمد (ورش)', folder: 'Abdul_Basit_Murattal_64kbps', riwaya: 'ورش' },
  { name: 'عبد الباسط عبد الصمد (ورش - جودة عالية)', folder: 'Abdul_Basit_Murattal_192kbps', riwaya: 'ورش' },
  { name: 'عبد الباسط عبد الصمد (ورش - مجود)', folder: 'Abdul_Basit_Mujawwad_128kbps', riwaya: 'ورش' },
  { name: 'إبراهيم الدوسري', folder: 'Ibrahim_Al-Dosari_64kbps', riwaya: 'ورش' },
  { name: 'إبراهيم الدوسري', folder: 'Ibrahim_Al-Dosari_128kbps', riwaya: 'ورش' },
  { name: 'عبد الباسط عبد الصمد (ورش - معلم)', folder: 'Abdul_Basit_Muallim_128kbps', riwaya: 'ورش' }
];

// دالة فحص وجود ملف mp3 بدقة عالية
function checkFileExists(url) {
  return new Promise((resolve) => {
    const req = https.get(url, (res) => {
      const contentLength = parseInt(res.headers['content-length'] || '0');
      const contentType = res.headers['content-type'] || '';
      
      // فحص دقيق للملف
      let isValid = false;
      let reason = '';
      
      if (res.statusCode === 200) {
        // فحص حجم الملف (أقل من 3KB = ملف فارغ أو خطأ)
        if (contentLength < 3000) {
          reason = `ملف صغير جداً (${contentLength} bytes)`;
        }
        // فحص نوع المحتوى
        else if (!contentType.includes('audio') && !contentType.includes('mp3')) {
          reason = `نوع محتوى غير صحيح: ${contentType}`;
        }
        else {
          isValid = true;
          reason = 'ملف صحيح';
        }
      } else {
        reason = `HTTP ${res.statusCode}`;
      }
      
      resolve({
        exists: res.statusCode === 200,
        isValid: isValid,
        statusCode: res.statusCode,
        contentLength: contentLength,
        contentType: contentType,
        reason: reason
      });
    });
    
    req.on('error', (err) => {
      resolve({
        exists: false,
        isValid: false,
        statusCode: 0,
        contentLength: 0,
        contentType: '',
        reason: `خطأ في الاتصال: ${err.message}`
      });
    });
    
    req.setTimeout(10000, () => {
      req.destroy();
      resolve({
        exists: false,
        isValid: false,
        statusCode: 0,
        contentLength: 0,
        contentType: '',
        reason: 'انتهت مهلة الاتصال'
      });
    });
  });
}

// دالة فحص قارئ واحد
async function checkReciter(reciter) {
  console.log(`\n🔍 فحص القارئ: ${reciter.name} (${reciter.riwaya})`);
  console.log(`📁 المجلد: ${reciter.folder}`);
  
  const results = [];
  const surahsToCheck = [1, 2, 3]; // الفاتحة، البقرة، آل عمران فقط
  
  for (const surah of surahsToCheck) {
    const surahStr = surah.toString().padStart(3, '0');
    const url = `https://everyayah.com/data/${reciter.folder}/${surahStr}001.mp3`;
    
    console.log(`  📖 فحص سورة ${surah}...`);
    const result = await checkFileExists(url);
    
    results.push({
      surah,
      url,
      ...result
    });
    
    // تأخير صغير لتجنب الضغط على السيرفر
    await new Promise(resolve => setTimeout(resolve, 200));
  }
  
  // تحليل النتائج
  const missingFiles = results.filter(r => !r.exists);
  const invalidFiles = results.filter(r => r.exists && !r.isValid);
  const validFiles = results.filter(r => r.exists && r.isValid);
  
  console.log(`✅ الملفات الصحيحة: ${validFiles.length}/${results.length}`);
  console.log(`❌ الملفات المفقودة: ${missingFiles.length}`);
  console.log(`⚠️ الملفات غير الصحيحة: ${invalidFiles.length}`);
  
  if (missingFiles.length > 0) {
    console.log(`📋 السور المفقودة: ${missingFiles.map(r => r.surah).join(', ')}`);
  }
  
  if (invalidFiles.length > 0) {
    console.log(`⚠️ السور غير الصحيحة: ${invalidFiles.map(r => `${r.surah} (${r.reason})`).join(', ')}`);
  }
  
  return {
    reciter,
    results,
    missingFiles,
    invalidFiles,
    validFiles,
    hasIssues: missingFiles.length > 0 || invalidFiles.length > 0
  };
}

// الدالة الرئيسية
async function main() {
  console.log('🎯 بدء الفحص الدقيق للبسملة في 3 سور فقط...\n');
  
  const allResults = [];
  const recitersWithIssues = [];
  
  for (const reciter of RECITERS) {
    const result = await checkReciter(reciter);
    allResults.push(result);
    
    if (result.hasIssues) {
      recitersWithIssues.push(result);
    }
  }
  
  // التقرير النهائي
  console.log('\n' + '='.repeat(80));
  console.log('📊 التقرير النهائي');
  console.log('='.repeat(80));
  
  console.log(`\n📈 إجمالي القراء المفحوصين: ${RECITERS.length}`);
  console.log(`❌ القراء الذين لديهم مشاكل: ${recitersWithIssues.length}`);
  console.log(`✅ القراء السليمين: ${RECITERS.length - recitersWithIssues.length}`);
  
  if (recitersWithIssues.length > 0) {
    console.log('\n🚨 القراء الذين يحتاجون إضافة بسملة:');
    console.log('-'.repeat(50));
    
    recitersWithIssues.forEach((result, index) => {
      console.log(`\n${index + 1}. ${result.reciter.name} (${result.reciter.riwaya})`);
      console.log(`   📁 المجلد: ${result.reciter.folder}`);
      console.log(`   ❌ ملفات مفقودة: ${result.missingFiles.length}`);
      console.log(`   ⚠️ ملفات غير صحيحة: ${result.invalidFiles.length}`);
      
      if (result.missingFiles.length > 0) {
        const missingSurahs = result.missingFiles.map(r => r.surah);
        console.log(`   📋 السور المفقودة: ${missingSurahs.join(', ')}`);
      }
      
      if (result.invalidFiles.length > 0) {
        const invalidDetails = result.invalidFiles.map(r => `${r.surah} (${r.reason})`);
        console.log(`   ⚠️ السور غير الصحيحة: ${invalidDetails.join(', ')}`);
      }
    });
  } else {
    console.log('\n✅ جميع القراء لديهم بسملة سليمة!');
  }
  
  // حفظ النتائج في ملف
  const fs = require('fs');
  const report = {
    totalReciters: RECITERS.length,
    recitersWithIssues: recitersWithIssues.length,
    recitersWithoutIssues: RECITERS.length - recitersWithIssues.length,
    recitersWithIssuesDetails: recitersWithIssues.map(r => ({
      name: r.reciter.name,
      folder: r.reciter.folder,
      riwaya: r.reciter.riwaya,
      missingFiles: r.missingFiles.length,
      invalidFiles: r.invalidFiles.length,
      missingSurahs: r.missingFiles.map(f => f.surah),
      invalidSurahs: r.invalidFiles.map(f => ({ surah: f.surah, reason: f.reason }))
    })),
    timestamp: new Date().toISOString()
  };
  
  fs.writeFileSync('accurate_basmala_report.json', JSON.stringify(report, null, 2));
  console.log('\n💾 تم حفظ التقرير في ملف: accurate_basmala_report.json');
}

main().catch(console.error); 