/**
 * اختبارات التوافق مع المتصفحات المختلفة
 * Browser Compatibility Tests
 */

import { QuranSettings } from '../hooks/useQuranSettings';

const STORAGE_KEY = 'quran-settings';

/**
 * اختبار دعم localStorage في المتصفح
 */
export const testLocalStorageSupport = (): boolean => {
  console.log('🧪 اختبار دعم localStorage...');
  
  try {
    // التحقق من وجود localStorage
    if (typeof Storage === 'undefined') {
      console.log('❌ localStorage غير مدعوم في هذا المتصفح');
      return false;
    }
    
    // اختبار الكتابة والقراءة
    const testKey = 'browser-test-key';
    const testValue = 'browser-test-value';
    
    localStorage.setItem(testKey, testValue);
    const retrievedValue = localStorage.getItem(testKey);
    localStorage.removeItem(testKey);
    
    if (retrievedValue === testValue) {
      console.log('✅ localStorage يعمل بشكل صحيح');
      return true;
    } else {
      console.log('❌ مشكلة في قراءة/كتابة localStorage');
      return false;
    }
    
  } catch (error) {
    console.log('❌ خطأ في localStorage:', error);
    return false;
  }
};

/**
 * اختبار دعم JSON في المتصفح
 */
export const testJSONSupport = (): boolean => {
  console.log('🧪 اختبار دعم JSON...');
  
  try {
    // التحقق من وجود JSON
    if (typeof JSON === 'undefined') {
      console.log('❌ JSON غير مدعوم في هذا المتصفح');
      return false;
    }
    
    // اختبار التحويل
    const testObject = {
      name: 'test',
      value: 123,
      array: [1, 2, 3],
      nested: { key: 'value' }
    };
    
    const jsonString = JSON.stringify(testObject);
    const parsedObject = JSON.parse(jsonString);
    
    // التحقق من صحة التحويل
    const isValid = (
      parsedObject.name === testObject.name &&
      parsedObject.value === testObject.value &&
      Array.isArray(parsedObject.array) &&
      parsedObject.array.length === 3 &&
      parsedObject.nested.key === testObject.nested.key
    );
    
    if (isValid) {
      console.log('✅ JSON يعمل بشكل صحيح');
      return true;
    } else {
      console.log('❌ مشكلة في تحويل JSON');
      return false;
    }
    
  } catch (error) {
    console.log('❌ خطأ في JSON:', error);
    return false;
  }
};

/**
 * اختبار دعم CSS المتقدم
 */
export const testAdvancedCSSSupport = (): boolean => {
  console.log('🧪 اختبار دعم CSS المتقدم...');
  
  try {
    // إنشاء عنصر تجريبي
    const testElement = document.createElement('div');
    testElement.style.position = 'absolute';
    testElement.style.top = '-9999px';
    testElement.style.left = '-9999px';
    
    // اختبار خصائص CSS المتقدمة
    const cssTests = [
      { property: 'borderRadius', value: '10px', name: 'Border Radius' },
      { property: 'boxShadow', value: '0 2px 4px rgba(0,0,0,0.1)', name: 'Box Shadow' },
      { property: 'transition', value: 'all 0.3s ease', name: 'CSS Transitions' },
      { property: 'transform', value: 'scale(1.1)', name: 'CSS Transforms' },
      { property: 'backdropFilter', value: 'blur(10px)', name: 'Backdrop Filter' }
    ];
    
    document.body.appendChild(testElement);
    
    let supportedFeatures = 0;
    
    for (const test of cssTests) {
      try {
        (testElement.style as any)[test.property] = test.value;
        const computedStyle = window.getComputedStyle(testElement);
        const appliedValue = (computedStyle as any)[test.property];
        
        if (appliedValue && appliedValue !== 'none' && appliedValue !== '') {
          console.log(`✅ ${test.name}: مدعوم`);
          supportedFeatures++;
        } else {
          console.log(`⚠️ ${test.name}: غير مدعوم`);
        }
      } catch (error) {
        console.log(`❌ ${test.name}: خطأ`);
      }
    }
    
    document.body.removeChild(testElement);
    
    // نعتبر النجاح إذا كان 80% من الميزات مدعومة
    const successThreshold = Math.ceil(cssTests.length * 0.8);
    const success = supportedFeatures >= successThreshold;
    
    console.log(`📊 ميزات CSS المدعومة: ${supportedFeatures}/${cssTests.length}`);
    
    if (success) {
      console.log('✅ دعم CSS متقدم كافي');
    } else {
      console.log('⚠️ دعم CSS محدود');
    }
    
    return success;
    
  } catch (error) {
    console.log('❌ خطأ في اختبار CSS:', error);
    return false;
  }
};

/**
 * اختبار دعم الخطوط العربية
 */
export const testArabicFontSupport = (): boolean => {
  console.log('🧪 اختبار دعم الخطوط العربية...');
  
  try {
    // إنشاء عنصر تجريبي
    const testElement = document.createElement('div');
    testElement.style.position = 'absolute';
    testElement.style.top = '-9999px';
    testElement.style.left = '-9999px';
    testElement.style.fontSize = '20px';
    testElement.textContent = 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ';
    
    // اختبار خطوط مختلفة
    const fonts = [
      'UthmanicHafs',
      'Amiri',
      'Arial',
      'Tahoma',
      'serif',
      'sans-serif'
    ];
    
    document.body.appendChild(testElement);
    
    let supportedFonts = 0;
    
    for (const font of fonts) {
      try {
        testElement.style.fontFamily = font;
        const computedStyle = window.getComputedStyle(testElement);
        const appliedFont = computedStyle.fontFamily;
        
        if (appliedFont && appliedFont.includes(font)) {
          console.log(`✅ خط ${font}: مدعوم`);
          supportedFonts++;
        } else {
          console.log(`⚠️ خط ${font}: غير متوفر (يستخدم: ${appliedFont})`);
        }
      } catch (error) {
        console.log(`❌ خط ${font}: خطأ`);
      }
    }
    
    document.body.removeChild(testElement);
    
    // نحتاج على الأقل خط واحد يعمل
    const success = supportedFonts > 0;
    
    console.log(`📊 الخطوط المدعومة: ${supportedFonts}/${fonts.length}`);
    
    if (success) {
      console.log('✅ دعم الخطوط العربية متوفر');
    } else {
      console.log('❌ مشكلة في دعم الخطوط العربية');
    }
    
    return success;
    
  } catch (error) {
    console.log('❌ خطأ في اختبار الخطوط:', error);
    return false;
  }
};

/**
 * اختبار دعم الأحداث (Events)
 */
export const testEventSupport = (): boolean => {
  console.log('🧪 اختبار دعم الأحداث...');
  
  try {
    // إنشاء عنصر تجريبي
    const testElement = document.createElement('button');
    testElement.style.position = 'absolute';
    testElement.style.top = '-9999px';
    testElement.style.left = '-9999px';
    
    document.body.appendChild(testElement);
    
    // اختبار أحداث مختلفة
    const events = [
      'click',
      'touchstart',
      'touchend',
      'keydown',
      'resize'
    ];
    
    let supportedEvents = 0;
    
    for (const eventName of events) {
      try {
        let eventFired = false;
        
        const handler = () => {
          eventFired = true;
        };
        
        testElement.addEventListener(eventName, handler);
        
        // محاكاة الحدث
        const event = new Event(eventName, { bubbles: true });
        testElement.dispatchEvent(event);
        
        testElement.removeEventListener(eventName, handler);
        
        if (eventFired) {
          console.log(`✅ حدث ${eventName}: مدعوم`);
          supportedEvents++;
        } else {
          console.log(`⚠️ حدث ${eventName}: غير مدعوم`);
        }
        
      } catch (error) {
        console.log(`❌ حدث ${eventName}: خطأ`);
      }
    }
    
    document.body.removeChild(testElement);
    
    // نحتاج على الأقل الأحداث الأساسية
    const requiredEvents = ['click', 'keydown'];
    const hasRequiredEvents = requiredEvents.every(eventName => 
      events.includes(eventName)
    );
    
    const success = supportedEvents >= 3 && hasRequiredEvents;
    
    console.log(`📊 الأحداث المدعومة: ${supportedEvents}/${events.length}`);
    
    if (success) {
      console.log('✅ دعم الأحداث كافي');
    } else {
      console.log('⚠️ دعم الأحداث محدود');
    }
    
    return success;
    
  } catch (error) {
    console.log('❌ خطأ في اختبار الأحداث:', error);
    return false;
  }
};

/**
 * اختبار معلومات المتصفح
 */
export const getBrowserInfo = (): { name: string; version: string; mobile: boolean } => {
  const userAgent = navigator.userAgent;
  
  let browserName = 'Unknown';
  let browserVersion = 'Unknown';
  
  // اكتشاف نوع المتصفح
  if (userAgent.includes('Chrome')) {
    browserName = 'Chrome';
    const match = userAgent.match(/Chrome\/(\d+)/);
    if (match) browserVersion = match[1];
  } else if (userAgent.includes('Firefox')) {
    browserName = 'Firefox';
    const match = userAgent.match(/Firefox\/(\d+)/);
    if (match) browserVersion = match[1];
  } else if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
    browserName = 'Safari';
    const match = userAgent.match(/Version\/(\d+)/);
    if (match) browserVersion = match[1];
  } else if (userAgent.includes('Edge')) {
    browserName = 'Edge';
    const match = userAgent.match(/Edge\/(\d+)/);
    if (match) browserVersion = match[1];
  }
  
  // اكتشاف الأجهزة المحمولة
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
  
  return {
    name: browserName,
    version: browserVersion,
    mobile: isMobile
  };
};

/**
 * تشغيل جميع اختبارات التوافق
 */
export const runAllCompatibilityTests = (): boolean => {
  console.log('🚀 بدء اختبارات التوافق مع المتصفحات...');
  
  // عرض معلومات المتصفح
  const browserInfo = getBrowserInfo();
  console.log(`🌐 المتصفح: ${browserInfo.name} ${browserInfo.version}`);
  console.log(`📱 جهاز محمول: ${browserInfo.mobile ? 'نعم' : 'لا'}`);
  
  const tests = [
    { name: 'دعم localStorage', test: testLocalStorageSupport },
    { name: 'دعم JSON', test: testJSONSupport },
    { name: 'دعم CSS المتقدم', test: testAdvancedCSSSupport },
    { name: 'دعم الخطوط العربية', test: testArabicFontSupport },
    { name: 'دعم الأحداث', test: testEventSupport }
  ];
  
  let passedTests = 0;
  
  for (const test of tests) {
    console.log(`\n--- اختبار ${test.name} ---`);
    if (test.test()) {
      passedTests++;
    }
  }
  
  const success = passedTests >= 4; // نحتاج على الأقل 4 من 5 اختبارات
  
  console.log('\n=== نتيجة اختبارات التوافق ===');
  if (success) {
    console.log(`🎉 المتصفح متوافق! (${passedTests}/${tests.length})`);
  } else {
    console.log(`⚠️ مشاكل في التوافق (${passedTests}/${tests.length})`);
  }
  
  return success;
};
