// سكربت مفصل لفحص مشاكل البسملة لكل قارئ
// يفحص كل قارئ من القائمة المحددة ويحدد المشكلة بدقة

const https = require('https');

// قائمة القراء الذين ليس لديهم بسملة (من الفحص اليدوي)
const RECITERS_WITHOUT_BASMALA = [
  // حفص عن عاصم
  { name: 'محمود خليل الحصري', folder: 'Husary_64kbps', riwaya: 'حفص', issue: 'غير محدد' },
  { name: 'محمود خليل الحصري', folder: 'Husary_128kbps', riwaya: 'حفص', issue: 'غير محدد' },
  { name: 'محمود خليل الحصري (مجود)', folder: 'Husary_Mujawwad_64kbps', riwaya: 'حفص', issue: 'غير محدد' },
  { name: 'محمود خليل الحصري (مجود)', folder: 'Husary_128kbps_Mujawwad', riwaya: 'حفص', issue: 'غير محدد' },
  { name: 'محمود خليل الحصري (معلم)', folder: 'Husary_Muallim_128kbps', riwaya: 'حفص', issue: 'غير محدد' },
  { name: 'محمد صديق المنشاوي (جودة منخفضة جداً)', folder: 'Menshawi_16kbps', riwaya: 'حفص', issue: 'غير محدد' },
  { name: 'محمد صديق المنشاوي (معلم)', folder: 'Minshawy_Muallim_128kbps', riwaya: 'حفص', issue: 'غير محدد' },
  { name: 'مصطفى إسماعيل', folder: 'Mustafa_Ismail_48kbps', riwaya: 'حفص', issue: 'غير شغال - يحتاج مراجعة' },
  { name: 'إبراهيم الأخضر', folder: 'Ibrahim_Akhdar_32kbps', riwaya: 'حفص', issue: 'غير محدد' },
  { name: 'هاني الرفاعي', folder: 'Hani_Rifai_64kbps', riwaya: 'حفص', issue: 'غير محدد' },
  { name: 'هاني الرفاعي (جودة عالية)', folder: 'Hani_Rifai_192kbps', riwaya: 'حفص', issue: 'غير محدد' },
  { name: 'ياسر سلامة', folder: 'Yaser_Salamah_128kbps', riwaya: 'حفص', issue: 'غير محدد' },
  { name: 'عبدالله بصفر', folder: 'Abdullah_Basfar_32kbps', riwaya: 'حفص', issue: 'غير محدد' },
  { name: 'عبدالله بصفر', folder: 'Abdullah_Basfar_64kbps', riwaya: 'حفص', issue: 'غير محدد' },
  { name: 'عبدالله بصفر (جودة عالية)', folder: 'Abdullah_Basfar_192kbps', riwaya: 'حفص', issue: 'غير محدد' },
  { name: 'علي عبدالرحمن الحذيفي', folder: 'Hudhaify_32kbps', riwaya: 'حفص', issue: 'غير محدد' },
  { name: 'علي عبدالرحمن الحذيفي', folder: 'Hudhaify_64kbps', riwaya: 'حفص', issue: 'غير محدد' },
  { name: 'خليفة الطنيجي', folder: 'khalefa_al_tunaiji_64kbps', riwaya: 'حفص', issue: 'غير محدد' },
  { name: 'أيمن سويد', folder: 'Ayman_Sowaid_64kbps', riwaya: 'حفص', issue: 'غير محدد' },
  { name: 'محمد أيوب', folder: 'Muhammad_Ayyoub_32kbps', riwaya: 'حفص', issue: 'غير محدد' },
  
  // ورش عن نافع
  { name: 'إبراهيم الدوسري', folder: 'warsh/warsh_ibrahim_aldosary_128kbps', riwaya: 'ورش', issue: 'غير محدد' },
  { name: 'عبد الباسط عبد الصمد (ورش)', folder: 'warsh/warsh_Abdul_Basit_128kbps', riwaya: 'ورش', issue: 'غير محدد' }
];

// دالة فحص وجود ملف mp3
function checkFileExists(url) {
  return new Promise((resolve) => {
    const req = https.get(url, (res) => {
      const contentLength = parseInt(res.headers['content-length'] || '0');
      const contentType = res.headers['content-type'] || '';
      
      resolve({
        exists: res.statusCode === 200,
        statusCode: res.statusCode,
        contentLength: contentLength,
        contentType: contentType,
        url: url
      });
    });
    
    req.on('error', (err) => {
      resolve({
        exists: false,
        statusCode: 0,
        contentLength: 0,
        contentType: '',
        error: err.message,
        url: url
      });
    });
    
    req.setTimeout(10000, () => {
      req.destroy();
      resolve({
        exists: false,
        statusCode: 0,
        contentLength: 0,
        contentType: '',
        error: 'انتهت مهلة الاتصال',
        url: url
      });
    });
  });
}

// دالة فحص قارئ واحد بالتفصيل
async function analyzeReciter(reciter) {
  console.log(`\n🔍 تحليل مفصل للقارئ: ${reciter.name} (${reciter.riwaya})`);
  console.log(`📁 المجلد: ${reciter.folder}`);
  console.log(`❓ المشكلة المذكورة: ${reciter.issue}`);
  
  const results = [];
  const surahsToCheck = [1, 2, 3]; // الفاتحة، البقرة، آل عمران
  
  for (const surah of surahsToCheck) {
    const surahStr = surah.toString().padStart(3, '0');
    const url = `https://everyayah.com/data/${reciter.folder}/${surahStr}001.mp3`;
    
    console.log(`  📖 فحص سورة ${surah} (${surahStr}001.mp3)...`);
    const result = await checkFileExists(url);
    
    results.push({
      surah,
      ...result
    });
    
    // تأخير صغير
    await new Promise(resolve => setTimeout(resolve, 200));
  }
  
  // تحليل النتائج
  const missingFiles = results.filter(r => !r.exists);
  const existingFiles = results.filter(r => r.exists);
  const smallFiles = existingFiles.filter(r => r.contentLength < 5000);
  
  console.log(`\n📊 النتائج:`);
  console.log(`✅ الملفات الموجودة: ${existingFiles.length}/${results.length}`);
  console.log(`❌ الملفات المفقودة: ${missingFiles.length}`);
  console.log(`⚠️ الملفات الصغيرة (<5KB): ${smallFiles.length}`);
  
  if (missingFiles.length > 0) {
    console.log(`📋 السور المفقودة: ${missingFiles.map(r => r.surah).join(', ')}`);
    missingFiles.forEach(r => {
      console.log(`   - سورة ${r.surah}: ${r.error || `HTTP ${r.statusCode}`}`);
    });
  }
  
  if (smallFiles.length > 0) {
    console.log(`⚠️ الملفات الصغيرة: ${smallFiles.map(r => `سورة ${r.surah} (${r.contentLength} bytes)`).join(', ')}`);
  }
  
  // تحديد نوع المشكلة
  let problemType = '';
  let solution = '';
  
  if (missingFiles.length === 3) {
    problemType = 'جميع الملفات مفقودة';
    solution = 'المجلد غير موجود أو القارئ غير متاح على السيرفر';
  } else if (missingFiles.length > 0) {
    problemType = 'بعض الملفات مفقودة';
    solution = 'قد تكون مشكلة في تسمية الملفات أو المجلد';
  } else if (smallFiles.length > 0) {
    problemType = 'ملفات صغيرة جداً';
    solution = 'الملفات موجودة لكن حجمها صغير جداً (قد تكون فارغة)';
  } else if (existingFiles.length === 3) {
    problemType = 'جميع الملفات موجودة';
    solution = 'قد تكون المشكلة في محتوى الملفات (لا تحتوي بسملة)';
  }
  
  console.log(`\n🎯 تحليل المشكلة:`);
  console.log(`📝 نوع المشكلة: ${problemType}`);
  console.log(`💡 الحل المقترح: ${solution}`);
  
  return {
    reciter,
    results,
    missingFiles,
    existingFiles,
    smallFiles,
    problemType,
    solution
  };
}

// الدالة الرئيسية
async function main() {
  console.log('🎯 بدء التحليل المفصل لمشاكل البسملة...\n');
  
  const allResults = [];
  
  for (const reciter of RECITERS_WITHOUT_BASMALA) {
    const result = await analyzeReciter(reciter);
    allResults.push(result);
  }
  
  // التقرير النهائي
  console.log('\n' + '='.repeat(80));
  console.log('📊 التقرير النهائي المفصل');
  console.log('='.repeat(80));
  
  // تجميع حسب نوع المشكلة
  const byProblemType = {};
  allResults.forEach(result => {
    if (!byProblemType[result.problemType]) {
      byProblemType[result.problemType] = [];
    }
    byProblemType[result.problemType].push(result);
  });
  
  console.log(`\n📈 إجمالي القراء المفحوصين: ${allResults.length}`);
  
  Object.entries(byProblemType).forEach(([problemType, results]) => {
    console.log(`\n🔍 ${problemType} (${results.length} قارئ):`);
    console.log('-'.repeat(50));
    
    results.forEach((result, index) => {
      console.log(`${index + 1}. ${result.reciter.name} (${result.reciter.riwaya})`);
      console.log(`   📁 المجلد: ${result.reciter.folder}`);
      console.log(`   💡 الحل: ${result.solution}`);
      
      if (result.missingFiles.length > 0) {
        const missingSurahs = result.missingFiles.map(r => r.surah);
        console.log(`   ❌ السور المفقودة: ${missingSurahs.join(', ')}`);
      }
      
      if (result.smallFiles.length > 0) {
        const smallDetails = result.smallFiles.map(r => `${r.surah} (${r.contentLength} bytes)`);
        console.log(`   ⚠️ الملفات الصغيرة: ${smallDetails.join(', ')}`);
      }
    });
  });
  
  // توصيات عامة
  console.log('\n' + '='.repeat(80));
  console.log('💡 التوصيات العامة');
  console.log('='.repeat(80));
  
  console.log('\n1. **القراء غير الشغالين:**');
  console.log('   - إزالة مصطفى إسماعيل من التطبيق');
  console.log('   - التحقق من وجود القراء الآخرين على السيرفر');
  
  console.log('\n2. **القراء المفقودين:**');
  console.log('   - إضافة القراء المفقودين (هاني الرفاعي، ياسر سلامة، إلخ)');
  console.log('   - التحقق من أسماء المجلدات الصحيحة');
  
  console.log('\n3. **معالجة البسملة:**');
  console.log('   - إضافة خيار "بدون بسملة" للقراء الذين ليس لديهم بسملة');
  console.log('   - تعديل الكود ليتعامل مع هذه الحالات');
  
  // حفظ النتائج في ملف
  const fs = require('fs');
  const report = {
    totalReciters: allResults.length,
    analysisByProblemType: byProblemType,
    detailedResults: allResults.map(r => ({
      name: r.reciter.name,
      folder: r.reciter.folder,
      riwaya: r.reciter.riwaya,
      problemType: r.problemType,
      solution: r.solution,
      missingFiles: r.missingFiles.length,
      smallFiles: r.smallFiles.length,
      missingSurahs: r.missingFiles.map(f => f.surah),
      smallFileDetails: r.smallFiles.map(f => ({ surah: f.surah, size: f.contentLength }))
    })),
    timestamp: new Date().toISOString()
  };
  
  fs.writeFileSync('detailed_basmala_analysis.json', JSON.stringify(report, null, 2));
  console.log('\n💾 تم حفظ التقرير المفصل في ملف: detailed_basmala_analysis.json');
}

main().catch(console.error); 