import { useState, useEffect, useCallback } from 'react';

export interface Reciter {
  id: string;
  name: string;
  nameAr: string;
  image: string;
  server: string;
  rewaya: string;
  count: number;
}

export interface DownloadProgress {
  surahId: number;
  progress: number;
  status: 'pending' | 'downloading' | 'completed' | 'error';
  error?: string;
}

export interface AudioFile {
  surahId: number;
  reciterId: string;
  fileName: string;
  filePath: string;
  size: number;
  duration: number;
  downloadedAt: string;
}

const RECITERS = [
  {
    id: 'yasser-dossari',
    name: '<PERSON><PERSON>',
    nameAr: 'ياسر الدوسري',
    image: '/reciter-images/yasser-dossari.jpg',
    server: 'https://server8.mp3quran.net/yasser_dossary/',
    rewaya: 'حفص عن عاصم',
    count: 114
  },
  {
    id: 'abdulbasit-abdulsamad',
    name: '<PERSON>',
    nameAr: 'عبد الباسط عبد الصمد',
    image: '/reciter-images/abdulbasit.jpg',
    server: 'https://server8.mp3quran.net/abdulbasit/',
    rewaya: 'حفص عن عاصم',
    count: 114
  }
];

const STORAGE_KEYS = {
  DOWNLOADED_FILES: 'quran-downloaded-files',
  DOWNLOAD_PROGRESS: 'quran-download-progress',
  SELECTED_RECITER: 'quran-selected-reciter'
};

export const useAudioDownload = () => {
  const [downloadedFiles, setDownloadedFiles] = useState<AudioFile[]>([]);
  const [downloadProgress, setDownloadProgress] = useState<DownloadProgress[]>([]);
  const [selectedReciter, setSelectedReciter] = useState<Reciter>(RECITERS[0]);
  const [isDownloading, setIsDownloading] = useState(false);

  // تحميل البيانات المحفوظة
  useEffect(() => {
    try {
      const savedFiles = localStorage.getItem(STORAGE_KEYS.DOWNLOADED_FILES);
      const savedProgress = localStorage.getItem(STORAGE_KEYS.DOWNLOAD_PROGRESS);
      const savedReciter = localStorage.getItem(STORAGE_KEYS.SELECTED_RECITER);

      if (savedFiles) {
        setDownloadedFiles(JSON.parse(savedFiles));
      }
      if (savedProgress) {
        setDownloadProgress(JSON.parse(savedProgress));
      }
      if (savedReciter) {
        const reciter = RECITERS.find(r => r.id === JSON.parse(savedReciter).id);
        if (reciter) {
          setSelectedReciter(reciter);
        }
      }
    } catch (error) {
      console.error('Error loading audio download data:', error);
    }
  }, []);

  // حفظ البيانات
  const saveData = useCallback(() => {
    try {
      localStorage.setItem(STORAGE_KEYS.DOWNLOADED_FILES, JSON.stringify(downloadedFiles));
      localStorage.setItem(STORAGE_KEYS.DOWNLOAD_PROGRESS, JSON.stringify(downloadProgress));
      localStorage.setItem(STORAGE_KEYS.SELECTED_RECITER, JSON.stringify(selectedReciter));
    } catch (error) {
      console.error('Error saving audio download data:', error);
    }
  }, [downloadedFiles, downloadProgress, selectedReciter]);

  useEffect(() => {
    saveData();
  }, [saveData]);

  // تحميل سورة واحدة
  const downloadSurah = useCallback(async (surahId: number, reciter: Reciter) => {
    if (typeof window === 'undefined') return;

    try {
      setIsDownloading(true);
      
      // تحديث حالة التحميل
      setDownloadProgress(prev => [
        ...prev.filter(p => !(p.surahId === surahId && p.status === 'completed')),
        { surahId, progress: 0, status: 'downloading' }
      ]);

      // إنشاء URL التحميل
      const fileName = `${surahId.toString().padStart(3, '0')}.mp3`;
      const downloadUrl = `${reciter.server}${fileName}`;

      console.log(`Downloading: ${downloadUrl}`);

      // استخدام Fetch API
      const response = await fetch(downloadUrl);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const blob = await response.blob();
      
      // حفظ الملف في localStorage (مؤقتاً)
      const filePath = `audio/${reciter.id}/${fileName}`;
      
      // إضافة الملف للقائمة المحملة
      const audioFile: AudioFile = {
        surahId,
        reciterId: reciter.id,
        fileName,
        filePath,
        size: blob.size,
        duration: 0,
        downloadedAt: new Date().toISOString()
      };

      setDownloadedFiles(prev => [
        ...prev.filter(f => !(f.surahId === surahId && f.reciterId === reciter.id)),
        audioFile
      ]);

      // تحديث حالة التحميل
      setDownloadProgress(prev => 
        prev.map(p => 
          p.surahId === surahId 
            ? { ...p, progress: 100, status: 'completed' }
            : p
        )
      );

      console.log(`Successfully downloaded: ${fileName}`);
      
    } catch (error) {
      console.error('Error downloading surah:', error);
      setDownloadProgress(prev => 
        prev.map(p => 
          p.surahId === surahId 
            ? { ...p, status: 'error', error: error instanceof Error ? error.message : 'Unknown error' }
            : p
        )
      );
    } finally {
      setIsDownloading(false);
    }
  }, []);

  // التحقق من وجود ملف محمل
  const isFileDownloaded = useCallback((surahId: number, reciterId: string) => {
    return downloadedFiles.some(f => f.surahId === surahId && f.reciterId === reciterId);
  }, [downloadedFiles]);

  // جلب URL الملف
  const getAudioUrl = useCallback((surahId: number, reciterId: string): string => {
    const reciter = RECITERS.find(r => r.id === reciterId);
    if (reciter) {
      const fileName = `${surahId.toString().padStart(3, '0')}.mp3`;
      return `${reciter.server}${fileName}`;
    }
    throw new Error('Reciter not found');
  }, []);

  return {
    reciters: RECITERS,
    downloadedFiles,
    downloadProgress,
    selectedReciter,
    isDownloading,
    downloadSurah,
    isFileDownloaded,
    getAudioUrl,
    setSelectedReciter
  };
}; 