/**
 * اختبارات شاملة لجميع أنواع الإعدادات
 * Comprehensive Settings Tests
 */

import { QuranSettings } from '../hooks/useQuranSettings';

const STORAGE_KEY = 'quran-settings';

/**
 * اختبار جميع أحجام النص المدعومة
 */
export const testAllFontSizes = (): boolean => {
  console.log('🧪 اختبار جميع أحجام النص...');
  
  const fontSizes = [14, 16, 18, 20, 22, 24, 26, 28, 30, 32];
  let passedTests = 0;
  
  for (const fontSize of fontSizes) {
    try {
      // إنشاء عنصر تجريبي
      const testElement = document.createElement('div');
      testElement.style.fontSize = `${fontSize}px`;
      testElement.style.fontFamily = 'UthmanicHafs, <PERSON>i, serif';
      testElement.textContent = 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ';
      
      // إضافة للصفحة
      document.body.appendChild(testElement);
      
      // قراءة الحجم المطبق
      const computedStyle = window.getComputedStyle(testElement);
      const appliedSize = parseInt(computedStyle.fontSize);
      
      // إزالة العنصر
      document.body.removeChild(testElement);
      
      if (appliedSize === fontSize) {
        console.log(`✅ حجم النص ${fontSize}px: نجح`);
        passedTests++;
      } else {
        console.log(`❌ حجم النص ${fontSize}px: فشل (المطبق: ${appliedSize}px)`);
      }
      
    } catch (error) {
      console.log(`❌ حجم النص ${fontSize}px: خطأ - ${error}`);
    }
  }
  
  const success = passedTests === fontSizes.length;
  console.log(`📊 نتيجة اختبار أحجام النص: ${passedTests}/${fontSizes.length}`);
  
  return success;
};

/**
 * اختبار جميع ألوان الثيم
 */
export const testAllThemeColors = (): boolean => {
  console.log('🧪 اختبار جميع ألوان الثيم...');
  
  const themes = [
    { name: 'red', color: '#ef4444', label: 'أحمر' },
    { name: 'gray', color: '#6b7280', label: 'رمادي' },
    { name: 'blue', color: '#3b82f6', label: 'أزرق' },
    { name: 'green', color: '#22c55e', label: 'أخضر' },
    { name: 'dark', color: '#1f2937', label: 'داكن' }
  ];
  
  let passedTests = 0;
  
  for (const theme of themes) {
    try {
      // إنشاء عنصر تجريبي
      const testElement = document.createElement('div');
      testElement.style.color = theme.color;
      testElement.textContent = `نص بلون ${theme.label}`;
      
      // إضافة للصفحة
      document.body.appendChild(testElement);
      
      // قراءة اللون المطبق
      const computedStyle = window.getComputedStyle(testElement);
      const appliedColor = computedStyle.color;
      
      // إزالة العنصر
      document.body.removeChild(testElement);
      
      // تحويل RGB إلى HEX للمقارنة
      const rgbToHex = (rgb: string): string => {
        const result = rgb.match(/\d+/g);
        if (!result) return '';
        
        const r = parseInt(result[0]);
        const g = parseInt(result[1]);
        const b = parseInt(result[2]);
        
        return `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`;
      };
      
      const appliedHex = rgbToHex(appliedColor);
      
      if (appliedHex.toLowerCase() === theme.color.toLowerCase()) {
        console.log(`✅ لون ${theme.label} (${theme.name}): نجح`);
        passedTests++;
      } else {
        console.log(`❌ لون ${theme.label} (${theme.name}): فشل (المطبق: ${appliedHex})`);
      }
      
    } catch (error) {
      console.log(`❌ لون ${theme.label}: خطأ - ${error}`);
    }
  }
  
  const success = passedTests === themes.length;
  console.log(`📊 نتيجة اختبار ألوان الثيم: ${passedTests}/${themes.length}`);
  
  return success;
};

/**
 * اختبار جميع أنواع محاذاة النص
 */
export const testAllTextAlignments = (): boolean => {
  console.log('🧪 اختبار جميع أنواع محاذاة النص...');
  
  const alignments = [
    { name: 'right', label: 'يمين', cssValue: 'right' },
    { name: 'justified', label: 'ضبط', cssValue: 'justify' }
  ];
  
  let passedTests = 0;
  
  for (const alignment of alignments) {
    try {
      // إنشاء عنصر تجريبي
      const testElement = document.createElement('div');
      testElement.style.textAlign = alignment.cssValue;
      testElement.style.width = '200px';
      testElement.textContent = 'هذا نص تجريبي لاختبار المحاذاة في النص العربي';
      
      // إضافة للصفحة
      document.body.appendChild(testElement);
      
      // قراءة المحاذاة المطبقة
      const computedStyle = window.getComputedStyle(testElement);
      const appliedAlignment = computedStyle.textAlign;
      
      // إزالة العنصر
      document.body.removeChild(testElement);
      
      if (appliedAlignment === alignment.cssValue) {
        console.log(`✅ محاذاة ${alignment.label} (${alignment.name}): نجح`);
        passedTests++;
      } else {
        console.log(`❌ محاذاة ${alignment.label} (${alignment.name}): فشل (المطبق: ${appliedAlignment})`);
      }
      
    } catch (error) {
      console.log(`❌ محاذاة ${alignment.label}: خطأ - ${error}`);
    }
  }
  
  const success = passedTests === alignments.length;
  console.log(`📊 نتيجة اختبار محاذاة النص: ${passedTests}/${alignments.length}`);
  
  return success;
};

/**
 * اختبار جميع أنواع الأرقام
 */
export const testAllNumberFormats = (): boolean => {
  console.log('🧪 اختبار جميع أنواع الأرقام...');
  
  const formats = [
    { name: 'arabic', label: 'عربي', testNumber: 123, expected: '١٢٣' },
    { name: 'english', label: 'إنجليزي', testNumber: 123, expected: '123' }
  ];
  
  // دالة تحويل الأرقام للعربية
  const convertToArabicNumbers = (num: number): string => {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return num.toString().split('').map(digit => arabicNumbers[parseInt(digit)]).join('');
  };
  
  let passedTests = 0;
  
  for (const format of formats) {
    try {
      let result: string;
      
      if (format.name === 'arabic') {
        result = convertToArabicNumbers(format.testNumber);
      } else {
        result = format.testNumber.toString();
      }
      
      if (result === format.expected) {
        console.log(`✅ تنسيق ${format.label} (${format.name}): نجح`);
        passedTests++;
      } else {
        console.log(`❌ تنسيق ${format.label} (${format.name}): فشل (النتيجة: ${result})`);
      }
      
    } catch (error) {
      console.log(`❌ تنسيق ${format.label}: خطأ - ${error}`);
    }
  }
  
  const success = passedTests === formats.length;
  console.log(`📊 نتيجة اختبار تنسيق الأرقام: ${passedTests}/${formats.length}`);
  
  return success;
};

/**
 * اختبار جميع أوضاع العرض
 */
export const testAllDisplayModes = (): boolean => {
  console.log('🧪 اختبار جميع أوضاع العرض...');
  
  const modes = [
    { name: 'dark', label: 'داكن' },
    { name: 'light', label: 'فاتح' },
    { name: 'auto', label: 'تلقائي' }
  ];
  
  let passedTests = 0;
  
  for (const mode of modes) {
    try {
      // محاكاة تطبيق وضع العرض
      const testSettings: Partial<QuranSettings> = {
        displayMode: mode.name as 'dark' | 'light' | 'auto'
      };
      
      // التحقق من صحة القيمة
      if (['dark', 'light', 'auto'].includes(mode.name)) {
        console.log(`✅ وضع ${mode.label} (${mode.name}): نجح`);
        passedTests++;
      } else {
        console.log(`❌ وضع ${mode.label} (${mode.name}): فشل`);
      }
      
    } catch (error) {
      console.log(`❌ وضع ${mode.label}: خطأ - ${error}`);
    }
  }
  
  const success = passedTests === modes.length;
  console.log(`📊 نتيجة اختبار أوضاع العرض: ${passedTests}/${modes.length}`);
  
  return success;
};

/**
 * تشغيل جميع الاختبارات الشاملة
 */
export const runAllComprehensiveTests = (): boolean => {
  console.log('🚀 بدء الاختبارات الشاملة لجميع الإعدادات...');
  
  const tests = [
    { name: 'أحجام النص', test: testAllFontSizes },
    { name: 'ألوان الثيم', test: testAllThemeColors },
    { name: 'محاذاة النص', test: testAllTextAlignments },
    { name: 'أنواع الأرقام', test: testAllNumberFormats },
    { name: 'أوضاع العرض', test: testAllDisplayModes }
  ];
  
  let passedTests = 0;
  
  for (const test of tests) {
    console.log(`\n--- اختبار ${test.name} ---`);
    if (test.test()) {
      passedTests++;
    }
  }
  
  const success = passedTests === tests.length;
  
  console.log('\n=== النتيجة النهائية ===');
  if (success) {
    console.log(`🎉 نجحت جميع الاختبارات الشاملة! (${passedTests}/${tests.length})`);
  } else {
    console.log(`⚠️ فشل في ${tests.length - passedTests} من ${tests.length} اختبارات`);
  }
  
  return success;
};
