// سكربت Node.js لفحص وجود البسملة (أول آية) لكل قارئ في كل السور (عدا التوبة)
// يعتمد على قائمة القراء من الكود (حفص وورش)
// النتيجة: قائمة القراء الذين ليس لديهم بسملة في أي سورة

const https = require('https');

// قائمة القراء (اسم، مجلد)
const RECITERS = [
  { name: 'عبد الباسط عبد الصمد (مرتل)', folder: 'Abdul_Basit_Murattal_64kbps' },
  { name: 'عبد الباسط عبد الصمد (مرتل - جودة عالية)', folder: 'Abdul_Basit_Murattal_192kbps' },
  { name: '<PERSON><PERSON><PERSON> <PERSON>لب<PERSON><PERSON>ط عبد الصمد (مجود)', folder: 'Abdul_<PERSON><PERSON>t_Mujawwad_128kbps' },
  { name: 'محمود خليل الحصري', folder: 'Husary_64kbps' },
  { name: 'محمود خليل الحصري', folder: 'Husary_128kbps' },
  { name: 'محمود خليل الحصري (مجود)', folder: 'Husary_Mujawwad_64kbps' },
  { name: 'محمود خليل الحصري (مجود)', folder: 'Husary_128kbps_Mujawwad' },
  { name: 'محمود خليل الحصري (معلم)', folder: 'Husary_Muallim_128kbps' },
  { name: 'محمد صديق المنشاوي (جودة منخفضة جداً)', folder: 'Menshawi_16kbps' },
  { name: 'محمد صديق المنشاوي (جودة منخفضة)', folder: 'Menshawi_32kbps' },
  { name: 'محمد صديق المنشاوي (مجود)', folder: 'Minshawy_Mujawwad_64kbps' },
  { name: 'محمد صديق المنشاوي (مرتل)', folder: 'Minshawy_Murattal_128kbps' },
  { name: 'محمد صديق المنشاوي (معلم)', folder: 'Minshawy_Teacher_128kbps' },
  { name: 'محمد صديق المنشاوي (مجود - جودة عالية)', folder: 'Minshawy_Mujawwad_192kbps' },
  { name: 'محمد الطبلاوي', folder: 'Mohammad_al_Tablaway_64kbps' },
  { name: 'محمد الطبلاوي', folder: 'Mohammad_al_Tablaway_128kbps' },
  { name: 'مصطفى إسماعيل', folder: 'Mustafa_Ismail_48kbps' },
  { name: 'إبراهيم الأخضر', folder: 'Ibrahim_Akhdar_32kbps' },
  { name: 'هاني الرفاعي', folder: 'Hani_Rifai_64kbps' },
  { name: 'هاني الرفاعي (جودة عالية)', folder: 'Hani_Rifai_192kbps' },
  { name: 'محمد جبريل', folder: 'Muhammad_Jibreel_64kbps' },
  { name: 'محمد جبريل', folder: 'Muhammad_Jibreel_128kbps' },
  { name: 'أحمد نعينع', folder: 'Ahmed_Neana_128kbps' },
  { name: 'محمد عبد الكريم', folder: 'Muhammad_AbdulKareem_128kbps' },
  { name: 'محمود علي البنا', folder: 'mahmoud_ali_al_banna_32kbps' },
  { name: 'علي حجاج السويسي', folder: 'Ali_Hajjaj_AlSuesy_128kbps' },
  { name: 'سهل ياسين', folder: 'Sahl_Yassin_128kbps' },
  { name: 'ياسر سلامة', folder: 'Yaser_Salamah_128kbps' },
  { name: 'عبد الرحمن السديس', folder: 'Abdurrahmaan_As-Sudais_64kbps' },
  { name: 'عبد الرحمن السديس (جودة عالية)', folder: 'Abdurrahmaan_As-Sudais_192kbps' },
  { name: 'سعود الشريم', folder: 'Saood_ash-Shuraym_64kbps' },
  { name: 'سعود الشريم', folder: 'Saood_ash-Shuraym_128kbps' },
  { name: 'ماهر المعيقلي', folder: 'Maher_AlMuaiqly_64kbps' },
  { name: 'ماهر المعيقلي', folder: 'MaherAlMuaiqly128kbps' },
  { name: 'ياسر الدوسري', folder: 'Yasser_Ad-Dussary_128kbps' },
  { name: 'عبد الله بصفر', folder: 'Abdullah_Basfar_32kbps' },
  { name: 'عبد الله بصفر', folder: 'Abdullah_Basfar_64kbps' },
  { name: 'عبد الله بصفر (جودة عالية)', folder: 'Abdullah_Basfar_192kbps' },
  { name: 'أحمد بن علي العجمي', folder: 'Ahmed_ibn_Ali_al-Ajamy_64kbps_QuranExplorer.Com' },
  { name: 'أحمد بن علي العجمي', folder: 'Ahmed_ibn_Ali_al-Ajamy_128kbps_ketaballah.net' },
  { name: 'سعد الغامدي', folder: 'Ghamadi_40kbps' },
  { name: 'علي عبد الرحمن الحذيفي', folder: 'Hudhaify_32kbps' },
  { name: 'علي عبد الرحمن الحذيفي', folder: 'Hudhaify_64kbps' },
  { name: 'علي عبد الرحمن الحذيفي', folder: 'Hudhaify_128kbps' },
  { name: 'ناصر القطامي', folder: 'Nasser_Alqatami_128kbps' },
  { name: 'عبد الله عواد الجهني', folder: 'Abdullaah_3awwaad_Al-Juhaynee_128kbps' },
  { name: 'صلاح البدير', folder: 'Salah_Al_Budair_128kbps' },
  { name: 'عبد الله مطرود', folder: 'Abdullah_Matroud_128kbps' },
  { name: 'خالد عبد الله القحطاني', folder: 'Khaalid_Abdullaah_al-Qahtaanee_192kbps' },
  { name: 'علي جابر', folder: 'Ali_Jaber_64kbps' },
  { name: 'مشاري راشد العفاسي', folder: 'Alafasy_64kbps' },
  { name: 'مشاري راشد العفاسي', folder: 'Alafasy_128kbps' },
  { name: 'فارس عباد', folder: 'Fares_Abbad_64kbps' },
  { name: 'صلاح عبد الرحمن بخاطر', folder: 'Salaah_AbdulRahman_Bukhatir_128kbps' },
  { name: 'خليفة الطنيجي', folder: 'khalefa_al_tunaiji_64kbps' },
  { name: 'أبو بكر الشاطري', folder: 'Abu_Bakr_Ash-Shaatree_64kbps' },
  { name: 'أبو بكر الشاطري', folder: 'Abu_Bakr_Ash-Shaatree_128kbps' },
  { name: 'محسن القاسمي', folder: 'Muhsin_Al_Qasim_192kbps' },
  { name: 'أكرم العلاقمي', folder: 'Akram_AlAlaqimy_128kbps' },
  { name: 'أيمن سويد', folder: 'Ayman_Sowaid_64kbps' },
  { name: 'نبيل الرفاعي', folder: 'Nabil_Rifa3i_48kbps' },
  { name: 'عزيز عليلي', folder: 'aziz_alili_128kbps' },
  { name: 'محمد أيوب', folder: 'Muhammad_Ayyoub_32kbps' },
  { name: 'محمد أيوب', folder: 'Muhammad_Ayyoub_64kbps' },
  { name: 'محمد أيوب', folder: 'Muhammad_Ayyoub_128kbps' },
  { name: 'كريم منصوري', folder: 'Karim_Mansoori_40kbps' },
  // ورش
  { name: 'إبراهيم الدوسري (ورش)', folder: 'warsh/warsh_ibrahim_aldosary_128kbps' },
  { name: 'ياسين الجزائري (ورش)', folder: 'warsh/warsh_yassin_al_jazaery_64kbps' },
  { name: 'عبد الباسط عبد الصمد (ورش)', folder: 'warsh/warsh_Abdul_Basit_128kbps' },
];

const SURAH_COUNT = 114;
const API_BASE = 'https://www.everyayah.com/data';
const MIN_SIZE = 10 * 1024; // 10KB

function checkUrl(url) {
  return new Promise((resolve) => {
    https.get(url, (res) => {
      let size = 0;
      res.on('data', chunk => size += chunk.length);
      res.on('end', () => {
        resolve({ status: res.statusCode, size });
      });
    }).on('error', () => {
      resolve({ status: 0, size: 0 });
    });
  });
}

(async () => {
  const result = [];
  for (const reciter of RECITERS) {
    let hasBasmalaProblem = false;
    for (let surah = 1; surah <= SURAH_COUNT; surah++) {
      if (surah === 9) continue; // استثناء التوبة
      const surahStr = surah.toString().padStart(3, '0');
      const ayahStr = '001';
      const url = `${API_BASE}/${reciter.folder}/${surahStr}${ayahStr}.mp3`;
      process.stdout.write(`\r[${reciter.name}] سورة ${surah} ...`);
      const { status, size } = await checkUrl(url);
      if (status !== 200 || size < MIN_SIZE) {
        hasBasmalaProblem = true;
        break;
      }
    }
    if (hasBasmalaProblem) {
      result.push({ name: reciter.name, folder: reciter.folder });
    }
  }
  console.log('\n---\nالقراء الذين ليس لديهم بسملة (أو هناك مشكلة في أول آية):');
  result.forEach(r => console.log(`- ${r.name} — ${r.folder}`));
  if (result.length === 0) {
    console.log('كل القراء لديهم بسملة في أول آية (عدا التوبة)');
  }
})(); 