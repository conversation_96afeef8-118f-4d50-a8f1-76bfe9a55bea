import React from 'react';
import QuranPageViewer from './QuranPageViewer';
import { useAudioPlayer } from '../hooks/useAudioPlayer';
import { QuranSettings } from '../hooks/useQuranSettings';

interface PageReaderProps {
  initialPage?: number;
  onBack?: () => void;
  onShowSettings?: () => void;
  targetAyahInfo?: { page: number, surah: number, ayah: number } | null;
  onClearTargetAyahInfo?: () => void;
  showPlaySettings?: boolean;
  setShowPlaySettings?: React.Dispatch<React.SetStateAction<boolean>>;
  webAudioCurrentAyah?: { surah: string, ayah: number } | null;
  audioPlayer: ReturnType<typeof useAudioPlayer>;
}

const PageReader: React.FC<PageReaderProps> = ({ initialPage = 1, onBack, onShowSettings, targetAyahInfo, onClearTargetAyahInfo, showPlaySettings, setShowPlaySettings, webAudioCurrentAyah, audioPlayer }) => {
  const [currentPage, setCurrentPage] = React.useState(initialPage);
  // const audioPlayer = useAudioPlayer(); // لا تستخدم hook محلي هنا
  const audioPlayerSettings = audioPlayer.settings;

  return (
    <QuranPageViewer
      pageNumber={currentPage}
      onBack={onBack || (() => {})}
      onPageChange={setCurrentPage}
      totalPages={604}
      onShowSettings={onShowSettings}
      targetAyahInfo={targetAyahInfo}
      onClearTargetAyahInfo={onClearTargetAyahInfo}
      showPlaySettings={showPlaySettings}
      setShowPlaySettings={setShowPlaySettings}
      webAudioCurrentAyah={webAudioCurrentAyah}
      audioPlayerSettings={audioPlayerSettings}
      audioPlayer={audioPlayer}
    />
  );
};

export default PageReader;
