import React, { useState } from 'react';
import ayatData from '../data/ayahobject.json';
import '../styles/quran-page.css';

interface Ayah {
  surah: number;
  ayah: number;
  text: string;
  page: number;
  juz?: number;
  hizb?: number;
  rub?: number;
  sajda?: boolean;
  surah_name?: string; // في بعض الملفات قد يكون اسم السورة موجودًا
}

const TOTAL_PAGES = 604;

// قائمة أسماء السور (للاستخدام في حال عدم وجودها في البيانات)
const surahNames = [
  '',
  'الفاتحة', 'البقرة', 'آل عمران', 'النساء', 'المائدة', 'الأنعام', 'الأعراف', 'الأنفال', 'التوبة', 'يونس',
  'هود', 'يوسف', 'الرعد', 'إبراهيم', 'الحجر', 'النحل', 'الإسراء', 'الكهف', 'مريم', 'طه',
  'الأنبياء', 'الحج', 'المؤمنون', 'النور', 'الفرقان', 'الشعراء', 'النمل', 'القصص', 'العنكبوت', 'الروم',
  'لقمان', 'السجدة', 'الأحزاب', 'سبأ', 'فاطر', 'يس', 'الصافات', 'ص', 'الزمر', 'غافر',
  'فصلت', 'الشورى', 'الزخرف', 'الدخان', 'الجاثية', 'الأحقاف', 'محمد', 'الفتح', 'الحجرات', 'ق',
  'الذاريات', 'الطور', 'النجم', 'القمر', 'الرحمن', 'الواقعة', 'الحديد', 'المجادلة', 'الحشر', 'الممتحنة',
  'الصف', 'الجمعة', 'المنافقون', 'التغابن', 'الطلاق', 'التحريم', 'الملك', 'القلم', 'الحاقة', 'المعارج',
  'نوح', 'الجن', 'المزمل', 'المدثر', 'القيامة', 'الإنسان', 'المرسلات', 'النبأ', 'النازعات', 'عبس',
  'التكوير', 'الانفطار', 'المطففين', 'الانشقاق', 'البروج', 'الطارق', 'الأعلى', 'الغاشية', 'الفجر', 'البلد',
  'الشمس', 'الليل', 'الضحى', 'الشرح', 'التين', 'العلق', 'القدر', 'البينة', 'الزلزلة', 'العاديات',
  'القارعة', 'التكاثر', 'العصر', 'الهمزة', 'الفيل', 'قريش', 'الماعون', 'الكوثر', 'الكافرون', 'النصر',
  'المسد', 'الإخلاص', 'الفلق', 'الناس'
];

const QURAN_BASMALA = 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ';

const QuranPage: React.FC = () => {
  const [page, setPage] = useState<number>(1);
  const [darkMode, setDarkMode] = useState<boolean>(false);

  // فلترة الآيات الخاصة بالصفحة الحالية
  const pageAyat = (ayatData as Ayah[]).filter((ayah) => ayah.page === page);

  // استخراج اسم السورة من أول آية في الصفحة
  const surahNumber = pageAyat.length > 0 ? pageAyat[0].surah : 1;
  const surahName = pageAyat[0]?.surah_name || surahNames[surahNumber];

  // استخراج رقم الجزء/الحزب من أول آية في الصفحة
  const juzNumber = pageAyat[0]?.juz;
  const hizbNumber = pageAyat[0]?.hizb;

  // منطق عرض البسملة المزخرفة (عدا سورة التوبة)
  const showBasmala =
    surahNumber !== 9 &&
    pageAyat.length > 0 &&
    pageAyat[0].ayah === 1 &&
    pageAyat[0].text.trim().startsWith(QURAN_BASMALA);

  // تغيير الصفحة
  const goToPage = (newPage: number) => {
    if (newPage >= 1 && newPage <= TOTAL_PAGES) setPage(newPage);
  };

  // منطق تحديد بداية ربع/حزب/جزء جديد
  let lastRub = -1;
  let lastHizb = -1;
  let lastJuz = -1;

  return (
    <div className={darkMode ? 'quran-page-container dark' : 'quran-page-container'}>
      <div className="quran-page-controls">
        <button onClick={() => setDarkMode(!darkMode)} className="darkmode-toggle">
          {darkMode ? 'وضع النهار' : 'وضع الليل'}
        </button>
        <button onClick={() => goToPage(page - 1)} disabled={page === 1}>
          الصفحة السابقة
        </button>
        <span>صفحة {page} من {TOTAL_PAGES}</span>
        <button onClick={() => goToPage(page + 1)} disabled={page === TOTAL_PAGES}>
          الصفحة التالية
        </button>
      </div>
      {/* عنوان السورة */}
      {surahName && (
        <div className="surah-header">
          <div className="surah-name">{surahName}</div>
        </div>
      )}

      {/* البسملة */}
      {showBasmala && (
        <div className="basmala">
          {QURAN_BASMALA}
        </div>
      )}

      <div className="quran-page-content">
        {pageAyat.map((ayah, idx) => {
          const showRub = ayah.rub !== undefined && ayah.rub !== lastRub;
          const showHizb = ayah.hizb !== undefined && ayah.hizb !== lastHizb;
          const showJuz = ayah.juz !== undefined && ayah.juz !== lastJuz;
          const showSajda = ayah.sajda === true;

          // تحديث القيم السابقة
          if (showRub) lastRub = ayah.rub!;
          if (showHizb) lastHizb = ayah.hizb!;
          if (showJuz) lastJuz = ayah.juz!;

          return (
            <span key={idx} className="ayah">
              {ayah.text}
              <span className="ayah-number">﴿{ayah.ayah}﴾</span>
            </span>
          );
        })}
      </div>

      {/* رقم الصفحة */}
      <div className="page-number">
        {page}
      </div>
    </div>
  );
};

export default QuranPage; 