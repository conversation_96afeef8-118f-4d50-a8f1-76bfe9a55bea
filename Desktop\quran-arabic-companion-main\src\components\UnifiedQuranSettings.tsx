import { useState } from "react";
import {
  <PERSON>R<PERSON>,
  RotateCcw,
  <PERSON>,
  Palette,
  Monitor,
  Hash,
  AlignRight,
  Eye,
  BookOpen,
  Settings,
  <PERSON>rkles,
  X,
  Save
} from "lucide-react";
import { useQuranSettings } from "../hooks/useQuranSettings";

interface UnifiedQuranSettingsProps {
  onBack: () => void;
  onFontSizeChange?: (size: number) => void;
  onSettingsChange?: (settings: any) => void;
  isModal?: boolean;
  onClose?: () => void;
}

const UnifiedQuranSettings = ({ 
  onBack, 
  onFontSizeChange, 
  onSettingsChange,
  isModal = false,
  onClose 
}: UnifiedQuranSettingsProps) => {
  const {
    settings,
    updateMushafType,
    updateColorTheme,
    updateDisplayMode,
    updateFontSize,
    updateNumberFormat,
    updateTextAlignment,
    toggleSurahNames,
    toggleJuzInfo,
    resetSettings,
    getBackgroundColorClass
  } = useQuranSettings();

  const [pageNumberInput, setPageNumberInput] = useState('1234');

  const handleFontSizeChange = (increment: number) => {
    const newSize = Math.max(14, Math.min(32, settings.fontSize + increment));
    updateFontSize(newSize);
    onFontSizeChange && onFontSizeChange(newSize);
  };

  const handleFontSizeReset = () => {
    updateFontSize(20);
    onFontSizeChange && onFontSizeChange(20);
  };

  const colors = [
    { name: 'red', color: 'bg-red-500', label: 'أحمر' },
    { name: 'gray', color: 'bg-gray-400', label: 'رمادي' },
    { name: 'blue', color: 'bg-blue-500', label: 'أزرق' },
    { name: 'green', color: 'bg-green-500', label: 'أخضر' },
    { name: 'dark', color: 'bg-gray-600', label: 'داكن' }
  ];

  const content = (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white">
      {/* Enhanced Header */}
      <div className="bg-gray-900/80 backdrop-blur-sm border-b border-gray-700 p-4 sticky top-0 z-10">
        <div className="flex items-center justify-between">
          <button
            onClick={resetSettings}
            className="flex items-center gap-2 px-3 py-2 rounded-lg text-gray-400 hover:text-white hover:bg-gray-800 transition-all duration-200"
            title="إعادة تعيين الإعدادات"
          >
            <RotateCcw size={18} />
            <span className="text-sm hidden sm:inline">إعادة تعيين</span>
          </button>

          <div className="flex items-center gap-3">
            <Settings size={24} className="text-green-400" />
            <h1 className="text-xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
              إعدادات المصحف الموحدة
            </h1>
          </div>

          <div className="flex items-center gap-2">
            <button
              onClick={onBack}
              className="flex items-center gap-2 px-3 py-2 rounded-lg text-green-400 hover:text-green-300 hover:bg-green-400/10 transition-all duration-200"
            >
              <span className="text-sm hidden sm:inline">رجوع</span>
              <ArrowRight size={18} />
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="p-6 space-y-8 max-w-4xl mx-auto">

        {/* Preview Section */}
        <div className="bg-gray-800/50 backdrop-blur-sm rounded-2xl p-6 border border-gray-700">
          <div className="flex items-center gap-3 mb-4">
            <Eye className="text-blue-400" size={20} />
            <h2 className="text-lg font-semibold text-white">معاينة النص المباشرة</h2>
          </div>
          <div className="bg-gray-900/50 rounded-xl p-6 text-center">
            <div
              className="text-right leading-relaxed"
              style={{
                fontFamily: 'UthmanicHafs, Amiri, serif',
                fontSize: `${settings.fontSize}px`,
                textAlign: settings.textAlignment === 'justified' ? 'justify' : 'right',
                color: settings.colorTheme === 'red' ? '#ef4444' :
                       settings.colorTheme === 'blue' ? '#3b82f6' :
                       settings.colorTheme === 'green' ? '#10b981' :
                       settings.colorTheme === 'dark' ? '#ffffff' : '#6b7280'
              }}
            >
              بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ
              <span
                className="mx-2"
                style={{
                  color: settings.colorTheme === 'red' ? '#ef4444' :
                         settings.colorTheme === 'blue' ? '#3b82f6' :
                         settings.colorTheme === 'green' ? '#10b981' :
                         settings.colorTheme === 'dark' ? '#ffffff' : '#6b7280'
                }}
              >
                {settings.numberFormat === 'arabic' ? '١' : '1'}
              </span>
              الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ
            </div>
            <div className="mt-4 text-xs text-gray-500">
              هذه المعاينة تتغير فوراً مع تغيير الإعدادات
            </div>
          </div>
        </div>

        {/* Content Settings Section */}
        <div className="bg-gray-800/50 backdrop-blur-sm rounded-2xl p-6 border border-gray-700">
          <div className="flex items-center gap-3 mb-6">
            <BookOpen className="text-green-400" size={20} />
            <h2 className="text-lg font-semibold text-white">إعدادات المحتوى</h2>
          </div>

          {/* Mushaf Type */}
          <div className="mb-6">
            <h3 className="text-white font-medium mb-3 text-right flex items-center gap-2">
              <span>نوع المصحف</span>
            </h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              <div
                onClick={() => updateMushafType('connected')}
                className={`rounded-xl p-4 text-right cursor-pointer transition-all duration-200 border ${
                  settings.mushafType === 'connected'
                    ? 'bg-green-500/20 border-green-500 shadow-lg shadow-green-500/20'
                    : 'bg-gray-700/50 border-gray-600 hover:bg-gray-700 hover:border-gray-500'
                }`}
              >
                <div className="flex items-center justify-between">
                  <span className={`text-2xl ${settings.mushafType === 'connected' ? 'text-green-400' : 'text-gray-400'}`}>
                    {settings.mushafType === 'connected' ? '✓' : '○'}
                  </span>
                  <div className="text-right">
                    <div className="text-white font-medium">آيات متصلة</div>
                    <div className="text-gray-400 text-sm">النص متصل بدون فواصل</div>
                  </div>
                </div>
              </div>

              <div
                onClick={() => updateMushafType('separate')}
                className={`rounded-xl p-4 text-right cursor-pointer transition-all duration-200 border ${
                  settings.mushafType === 'separate'
                    ? 'bg-green-500/20 border-green-500 shadow-lg shadow-green-500/20'
                    : 'bg-gray-700/50 border-gray-600 hover:bg-gray-700 hover:border-gray-500'
                }`}
              >
                <div className="flex items-center justify-between">
                  <span className={`text-2xl ${settings.mushafType === 'separate' ? 'text-green-400' : 'text-gray-400'}`}>
                    {settings.mushafType === 'separate' ? '✓' : '○'}
                  </span>
                  <div className="text-right">
                    <div className="text-white font-medium">آيات منفصلة</div>
                    <div className="text-gray-400 text-sm">كل آية منفصلة</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Typography Settings Section */}
        <div className="bg-gray-800/50 backdrop-blur-sm rounded-2xl p-6 border border-gray-700">
          <div className="flex items-center gap-3 mb-6">
            <Type className="text-purple-400" size={20} />
            <h2 className="text-lg font-semibold text-white">إعدادات النص والخط</h2>
          </div>

          {/* Font Size */}
          <div className="mb-6">
            <h3 className="text-white font-medium mb-4 text-right flex items-center gap-2">
              <span>حجم النص</span>
              <span className="text-purple-400 text-sm">({settings.fontSize}px)</span>
            </h3>
            <div className="bg-gray-700/50 rounded-xl p-4">
              <div className="flex justify-center items-center gap-6">
                <button
                  onClick={() => handleFontSizeChange(-2)}
                  className="w-12 h-12 bg-gray-600 hover:bg-gray-500 rounded-full transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center shadow-lg"
                  disabled={settings.fontSize <= 14}
                >
                  <span className="text-xl font-bold text-white">-</span>
                </button>

                <div className="flex flex-col items-center gap-3">
                  <div className="bg-gray-800 rounded-lg px-4 py-2 min-w-[80px] text-center">
                    <span className="text-white text-xl font-bold">{settings.fontSize}</span>
                  </div>
                  <button
                    onClick={handleFontSizeReset}
                    className="bg-purple-600 hover:bg-purple-500 text-white px-4 py-2 rounded-lg transition-all duration-200 text-sm font-medium shadow-lg"
                  >
                    الافتراضي (20)
                  </button>
                </div>

                <button
                  onClick={() => handleFontSizeChange(2)}
                  className="w-12 h-12 bg-gray-600 hover:bg-gray-500 rounded-full transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center shadow-lg"
                  disabled={settings.fontSize >= 32}
                >
                  <span className="text-xl font-bold text-white">+</span>
                </button>
              </div>

              {/* Font Size Range Indicator */}
              <div className="mt-4 flex justify-between text-xs text-gray-400">
                <span>صغير (14)</span>
                <span>متوسط (20)</span>
                <span>كبير (32)</span>
              </div>
              <div className="mt-2 w-full bg-gray-600 rounded-full h-2">
                <div
                  className="bg-purple-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${((settings.fontSize - 14) / (32 - 14)) * 100}%` }}
                ></div>
              </div>
            </div>
          </div>

          {/* Text Alignment */}
          <div className="mb-6">
            <h3 className="text-white font-medium mb-4 text-right flex items-center gap-2">
              <AlignRight className="text-purple-400" size={16} />
              <span>محاذاة النص</span>
            </h3>
            <div className="grid grid-cols-2 gap-4">
              <button
                onClick={() => updateTextAlignment('justified')}
                className={`px-4 py-3 rounded-xl transition-all duration-200 border ${
                  settings.textAlignment === 'justified'
                    ? 'bg-purple-500/20 border-purple-500 text-white shadow-lg shadow-purple-500/20'
                    : 'bg-gray-700/50 border-gray-600 text-gray-300 hover:bg-gray-700 hover:border-gray-500'
                }`}
              >
                <div className="text-center">
                  <div className="font-medium">ضبط تلقائي</div>
                  <div className="text-xs opacity-75 mt-1">توزيع متساوي</div>
                </div>
              </button>
              <button
                onClick={() => updateTextAlignment('right')}
                className={`px-4 py-3 rounded-xl transition-all duration-200 border ${
                  settings.textAlignment === 'right'
                    ? 'bg-purple-500/20 border-purple-500 text-white shadow-lg shadow-purple-500/20'
                    : 'bg-gray-700/50 border-gray-600 text-gray-300 hover:bg-gray-700 hover:border-gray-500'
                }`}
              >
                <div className="text-center">
                  <div className="font-medium">محاذاة يمين</div>
                  <div className="text-xs opacity-75 mt-1">محاذاة عادية</div>
                </div>
              </button>
            </div>
          </div>
        </div>

        {/* Appearance Settings Section */}
        <div className="bg-gray-800/50 backdrop-blur-sm rounded-2xl p-6 border border-gray-700">
          <div className="flex items-center gap-3 mb-6">
            <Palette className="text-pink-400" size={20} />
            <h2 className="text-lg font-semibold text-white">إعدادات المظهر والألوان</h2>
          </div>

          {/* Color Theme */}
          <div className="mb-6">
            <h3 className="text-white font-medium mb-4 text-right flex items-center gap-2">
              <Sparkles className="text-pink-400" size={16} />
              <span>لون الثيم</span>
            </h3>
            <div className="grid grid-cols-5 gap-4">
              {colors.map((color) => (
                <div
                  key={color.name}
                  onClick={() => updateColorTheme(color.name as any)}
                  className={`flex flex-col items-center gap-3 p-4 rounded-xl cursor-pointer transition-all duration-200 border ${
                    settings.colorTheme === color.name
                      ? 'bg-pink-500/20 border-pink-500 shadow-lg shadow-pink-500/20'
                      : 'bg-gray-700/50 border-gray-600 hover:bg-gray-700 hover:border-gray-500'
                  }`}
                >
                  <div className="relative">
                    <div
                      className={`w-10 h-10 ${color.color} rounded-full shadow-lg transition-transform duration-200 ${
                        settings.colorTheme === color.name ? 'scale-110' : 'hover:scale-105'
                      }`}
                    ></div>
                    {settings.colorTheme === color.name && (
                      <div className="absolute -top-1 -right-1 w-4 h-4 bg-pink-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-xs">✓</span>
                      </div>
                    )}
                  </div>
                  <span className="text-xs text-gray-300 font-medium">{color.label}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Display Mode */}
          <div>
            <h3 className="text-white font-medium mb-4 text-right flex items-center gap-2">
              <Monitor className="text-pink-400" size={16} />
              <span>وضع العرض</span>
            </h3>
            <div className="grid grid-cols-3 gap-4">
              <button
                onClick={() => updateDisplayMode('dark')}
                className={`px-4 py-4 rounded-xl transition-all duration-200 border ${
                  settings.displayMode === 'dark'
                    ? 'bg-pink-500/20 border-pink-500 text-white shadow-lg shadow-pink-500/20'
                    : 'bg-gray-700/50 border-gray-600 text-gray-300 hover:bg-gray-700 hover:border-gray-500'
                }`}
              >
                <div className="flex flex-col items-center gap-2">
                  <div className="w-8 h-8 bg-gray-800 rounded border border-gray-600 flex items-center justify-center">
                    <div className="w-3 h-3 bg-gray-400 rounded-full"></div>
                  </div>
                  <span className="font-medium">داكن</span>
                </div>
              </button>
              <button
                onClick={() => updateDisplayMode('light')}
                className={`px-4 py-4 rounded-xl transition-all duration-200 border ${
                  settings.displayMode === 'light'
                    ? 'bg-pink-500/20 border-pink-500 text-white shadow-lg shadow-pink-500/20'
                    : 'bg-gray-700/50 border-gray-600 text-gray-300 hover:bg-gray-700 hover:border-gray-500'
                }`}
              >
                <div className="flex flex-col items-center gap-2">
                  <div className="w-8 h-8 bg-white rounded border border-gray-300 flex items-center justify-center">
                    <div className="w-3 h-3 bg-gray-600 rounded-full"></div>
                  </div>
                  <span className="font-medium">فاتح</span>
                </div>
              </button>
              <button
                onClick={() => updateDisplayMode('auto')}
                className={`px-4 py-4 rounded-xl transition-all duration-200 border ${
                  settings.displayMode === 'auto'
                    ? 'bg-pink-500/20 border-pink-500 text-white shadow-lg shadow-pink-500/20'
                    : 'bg-gray-700/50 border-gray-600 text-gray-300 hover:bg-gray-700 hover:border-gray-500'
                }`}
              >
                <div className="flex flex-col items-center gap-2">
                  <div className="w-8 h-8 bg-gradient-to-r from-gray-800 to-white rounded border border-gray-400 flex items-center justify-center">
                    <div className="w-3 h-3 bg-gradient-to-r from-gray-400 to-gray-600 rounded-full"></div>
                  </div>
                  <span className="font-medium">تلقائي</span>
                </div>
              </button>
            </div>
          </div>
        </div>

        {/* Display Options Section */}
        <div className="bg-gray-800/50 backdrop-blur-sm rounded-2xl p-6 border border-gray-700">
          <div className="flex items-center gap-3 mb-6">
            <Sparkles className="text-cyan-400" size={20} />
            <h2 className="text-lg font-semibold text-white">خيارات العرض</h2>
          </div>
          <div className="space-y-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              {/* Show Surah Names */}
              <div
                onClick={toggleSurahNames}
                className={`rounded-xl p-4 text-right cursor-pointer transition-all duration-200 border ${
                  settings.showSurahNames
                    ? 'bg-cyan-500/20 border-cyan-500 shadow-lg shadow-cyan-500/20'
                    : 'bg-gray-700/50 border-gray-600 hover:bg-gray-700 hover:border-gray-500'
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center transition-all duration-200 ${
                    settings.showSurahNames
                      ? 'border-cyan-400 bg-cyan-400'
                      : 'border-gray-400'
                  }`}>
                    {settings.showSurahNames && (
                      <span className="text-white text-sm font-bold">✓</span>
                    )}
                  </div>
                  <div className="text-right">
                    <div className="text-white font-medium">إظهار أسماء السور</div>
                    <div className="text-gray-400 text-sm">عرض اسم السورة في الأعلى</div>
                  </div>
                </div>
              </div>

              {/* Show Juz Info */}
              <div
                onClick={toggleJuzInfo}
                className={`rounded-xl p-4 text-right cursor-pointer transition-all duration-200 border ${
                  settings.showJuzInfo
                    ? 'bg-cyan-500/20 border-cyan-500 shadow-lg shadow-cyan-500/20'
                    : 'bg-gray-700/50 border-gray-600 hover:bg-gray-700 hover:border-gray-500'
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center transition-all duration-200 ${
                    settings.showJuzInfo
                      ? 'border-cyan-400 bg-cyan-400'
                      : 'border-gray-400'
                  }`}>
                    {settings.showJuzInfo && (
                      <span className="text-white text-sm font-bold">✓</span>
                    )}
                  </div>
                  <div className="text-right">
                    <div className="text-white font-medium">إظهار معلومات الجزء</div>
                    <div className="text-gray-400 text-sm">عرض رقم الجزء والحزب</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer with additional info */}
        <div className="text-center text-gray-400 text-sm py-4">
          <p>جميع الإعدادات يتم حفظها تلقائياً وتطبيقها في جميع أجزاء التطبيق</p>
        </div>
      </div>
    </div>
  );

  // إذا كان modal، نعرض المحتوى في modal
  if (isModal) {
    return (
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
        <div className="bg-gray-900 rounded-2xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
          {content}
        </div>
      </div>
    );
  }

  return content;
};

export default UnifiedQuranSettings; 