import React, { useState, useEffect } from 'react';
import { useSwipeable } from 'react-swipeable';
import { Home, Moon, Sun, ChevronLeft, ChevronRight } from 'lucide-react';
import { quranData, suras } from '../data/quranData';

interface SimpleQuranViewerProps {
  pageNumber: number;
  onBack: () => void;
  onPageChange: (newPage: number) => void;
  totalPages?: number;
}

interface Verse {
  id: number;
  text: string;
}

interface Surah {
  id: number;
  name: string;
  verses: Verse[];
}

const SimpleQuranViewer: React.FC<SimpleQuranViewerProps> = ({
  pageNumber,
  onBack,
  onPageChange,
  totalPages = 604
}) => {
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [verses, setVerses] = useState<Verse[]>([]);
  const [loading, setLoading] = useState(true);
  const [surahName, setSurahName] = useState('');

  // Convert to Arabic numbers
  const convertToArabicNumbers = (num: number): string => {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return num.toString().split('').map(digit => arabicNumbers[parseInt(digit)]).join('');
  };

  // Load dark mode from localStorage
  useEffect(() => {
    const savedDarkMode = localStorage.getItem('quran-dark-mode');
    if (savedDarkMode) {
      setIsDarkMode(savedDarkMode === 'true');
    }
  }, []);

  // Save dark mode to localStorage
  useEffect(() => {
    localStorage.setItem('quran-dark-mode', isDarkMode.toString());
  }, [isDarkMode]);

  // Load verses for current page (simplified)
  useEffect(() => {
    const loadVerses = () => {
      setLoading(true);

      try {
        // Get verses for the current page (simplified approach)
        const startIndex = (pageNumber - 1) * 15;
        const endIndex = startIndex + 15;

        // Filter verses from quranData array
        const pageVerses = quranData
          .slice(startIndex, endIndex)
          .map((verse, index) => ({
            id: verse.ayah,
            text: verse.text
          }));

        // Get surah name from first verse
        const firstVerse = quranData[startIndex];
        let currentSurahName = '';

        if (firstVerse) {
          // Find surah name from suras data
          const surahInfo = suras.find(s => s.id === firstVerse.surah);
          currentSurahName = surahInfo ? surahInfo.arabicName : `سورة ${firstVerse.surah}`;
        }

        setVerses(pageVerses);
        setSurahName(currentSurahName);
      } catch (error) {
        console.error('Error loading verses:', error);
        setVerses([]);
        setSurahName('');
      }

      setLoading(false);
    };

    loadVerses();
  }, [pageNumber]);

  // Swipe handlers
  const handlers = useSwipeable({
    onSwipedLeft: () => pageNumber < totalPages && onPageChange(pageNumber + 1),
    onSwipedRight: () => pageNumber > 1 && onPageChange(pageNumber - 1),
    trackMouse: true,
  });

  if (loading) {
    return (
      <div className={`min-h-screen flex items-center justify-center ${
        isDarkMode ? 'bg-gray-900 text-white' : 'bg-white text-gray-900'
      }`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-amber-500 mx-auto mb-4"></div>
          <p className="text-lg">جاري التحميل...</p>
        </div>
      </div>
    );
  }

  return (
    <div 
      {...handlers}
      className={`min-h-screen transition-colors duration-300 ${
        isDarkMode 
          ? 'bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white' 
          : 'bg-gradient-to-br from-amber-50 via-white to-amber-50 text-gray-900'
      }`}
    >
      {/* Header */}
      <div className={`sticky top-0 z-10 px-4 py-3 border-b ${
        isDarkMode 
          ? 'bg-gray-800/90 border-gray-700 backdrop-blur-sm' 
          : 'bg-white/90 border-amber-200 backdrop-blur-sm'
      }`}>
        <div className="flex items-center justify-between">
          <button
            onClick={onBack}
            className={`p-2 rounded-full transition-colors ${
              isDarkMode 
                ? 'hover:bg-gray-700 text-amber-400' 
                : 'hover:bg-amber-100 text-amber-600'
            }`}
          >
            <Home size={24} />
          </button>

          <div className="text-center">
            <h1 className="text-lg font-bold">{surahName}</h1>
            <p className="text-sm opacity-70">الجزء الأول</p>
          </div>

          <button
            onClick={() => setIsDarkMode(!isDarkMode)}
            className={`p-2 rounded-full transition-colors ${
              isDarkMode 
                ? 'hover:bg-gray-700 text-amber-400' 
                : 'hover:bg-amber-100 text-amber-600'
            }`}
          >
            {isDarkMode ? <Sun size={24} /> : <Moon size={24} />}
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="px-6 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Verses */}
          <div className="space-y-6 leading-loose text-right" dir="rtl">
            {verses.map((verse, index) => (
              <div key={verse.id} className="flex items-start justify-end">
                <span className="text-2xl font-arabic leading-relaxed">
                  {verse.text}
                  <span className={`inline-flex items-center justify-center w-8 h-8 rounded-full text-sm font-bold mx-2 ${
                    isDarkMode 
                      ? 'bg-gradient-to-r from-amber-600 to-yellow-600 text-white border-2 border-amber-500 shadow-lg' 
                      : 'bg-gradient-to-r from-amber-200 to-yellow-200 text-amber-900 border-2 border-amber-400 shadow-lg'
                  }`}>
                    {convertToArabicNumbers(verse.id)}
                  </span>
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className={`fixed bottom-0 left-0 right-0 p-4 border-t ${
        isDarkMode 
          ? 'bg-gray-800/90 border-gray-700 backdrop-blur-sm' 
          : 'bg-white/90 border-amber-200 backdrop-blur-sm'
      }`}>
        <div className="flex items-center justify-between max-w-4xl mx-auto">
          <button
            onClick={() => pageNumber > 1 && onPageChange(pageNumber - 1)}
            disabled={pageNumber <= 1}
            className={`p-3 rounded-full transition-colors ${
              pageNumber <= 1 
                ? 'opacity-50 cursor-not-allowed' 
                : isDarkMode 
                  ? 'hover:bg-gray-700 text-amber-400' 
                  : 'hover:bg-amber-100 text-amber-600'
            }`}
          >
            <ChevronRight size={24} />
          </button>

          <div className="text-center">
            <span className="text-lg font-bold">
              {convertToArabicNumbers(pageNumber)}
            </span>
          </div>

          <button
            onClick={() => pageNumber < totalPages && onPageChange(pageNumber + 1)}
            disabled={pageNumber >= totalPages}
            className={`p-3 rounded-full transition-colors ${
              pageNumber >= totalPages 
                ? 'opacity-50 cursor-not-allowed' 
                : isDarkMode 
                  ? 'hover:bg-gray-700 text-amber-400' 
                  : 'hover:bg-amber-100 text-amber-600'
            }`}
          >
            <ChevronLeft size={24} />
          </button>
        </div>
      </div>
    </div>
  );
};

export default SimpleQuranViewer;
