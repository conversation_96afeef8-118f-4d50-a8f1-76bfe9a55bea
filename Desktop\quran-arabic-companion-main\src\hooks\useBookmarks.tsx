import { createContext, useContext, useEffect, useState, ReactNode } from 'react';

// Bookmark types
export type AyahBookmark = { // Exported
  type: 'ayah';
  surah: number;
  ayah: number;
  color: string;
};
export type PageBookmark = { // Exported
  type: 'page';
  page: number;
};
export type Bookmark = AyahBookmark | PageBookmark; // Exported

type BookmarksContextType = {
  bookmarks: Bookmark[];
  addAyahBookmark: (surah: number, ayah: number, color: string) => void;
  addPageBookmark: (page: number) => void;
  removeBookmark: (bookmark: Bookmark) => void;
  toggleAyahBookmark: (surah: number, ayah: number, color: string) => void;
  togglePageBookmark: (page: number) => void; // Added togglePageBookmark
  isAyahBookmarked: (surah: number, ayah: number, color?: string) => AyahBookmark | undefined;
  isPageBookmarked: (page: number) => boolean;
};

const BookmarksContext = createContext<BookmarksContextType | undefined>(undefined);

const LOCAL_STORAGE_KEY = 'quran_bookmarks';

export function BookmarksProvider({ children }: { children: ReactNode }) {
  const [bookmarks, setBookmarks] = useState<Bookmark[]>([]);

  // Load from localStorage
  useEffect(() => {
    const saved = localStorage.getItem(LOCAL_STORAGE_KEY);
    if (saved) {
      setBookmarks(JSON.parse(saved));
    }
  }, []);

  // Save to localStorage
  useEffect(() => {
    localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(bookmarks));
  }, [bookmarks]);

  // Add ayah bookmark (always adds a new one, allows multiple of same ayah with different colors)
  const addAyahBookmark = (surah: number, ayah: number, color: string) => {
    setBookmarks(prev => {
      // Check if an identical bookmark (same surah, ayah, and color) already exists
      const exists = prev.some(
        b => b.type === 'ayah' && b.surah === surah && b.ayah === ayah && b.color === color
      );
      if (exists) {
        return prev; // Do not add if identical bookmark already exists
      }
      return [...prev, { type: 'ayah', surah, ayah, color, timestamp: new Date().toISOString() }];
    });
  };

  // Add page bookmark
  const addPageBookmark = (page: number) => {
    setBookmarks(prev => {
      if (prev.some(b => b.type === 'page' && b.page === page)) return prev;
      return [...prev, { type: 'page', page, timestamp: new Date().toISOString() }];
    });
  };

  // Toggle page bookmark
  const togglePageBookmark = (page: number) => {
    setBookmarks(prev => {
      if (prev.some(b => b.type === 'page' && b.page === page)) {
        return prev.filter(b => !(b.type === 'page' && b.page === page));
      } else {
        return [...prev, { type: 'page', page, timestamp: new Date().toISOString() }];
      }
    });
  };

  // Remove bookmark
  const removeBookmark = (bookmark: Bookmark) => {
    setBookmarks(prev => prev.filter(b => {
      if (bookmark.type === 'ayah' && b.type === 'ayah') {
        // إذا كان bookmark به خاصية color، احذف فقط المطابق للسورة والآية واللون
        if ('color' in bookmark && 'color' in b) {
          return !(b.surah === bookmark.surah && b.ayah === bookmark.ayah && b.color === bookmark.color);
        }
        // إذا لم يوجد لون، احذف كل الفواصل لنفس الآية (سلوك احتياطي)
        return !(b.surah === bookmark.surah && b.ayah === bookmark.ayah);
      }
      if (bookmark.type === 'page' && b.type === 'page') {
        return b.page !== bookmark.page;
      }
      return true;
    }));
  };

  // Toggle ayah bookmark (add or remove specific color bookmark)
  const toggleAyahBookmark = (surah: number, ayah: number, color: string) => {
    setBookmarks(prev => {
      const existingBookmark = prev.find(
        b => b.type === 'ayah' && b.surah === surah && b.ayah === ayah && b.color === color
      );

      if (existingBookmark) {
        // Remove the specific bookmark if it exists
        return prev.filter(b => !(b.type === 'ayah' && b.surah === surah && b.ayah === ayah && b.color === color));
      } else {
        // Add a new bookmark
        return [...prev, { type: 'ayah', surah, ayah, color, timestamp: new Date().toISOString() }];
      }
    });
  };

  // Check if ayah is bookmarked with a specific color, or any color if color is not provided
  const isAyahBookmarked = (surah: number, ayah: number, color?: string) => {
    if (color) {
      return bookmarks.find(
        b => b.type === 'ayah' && b.surah === surah && b.ayah === ayah && b.color === color
      ) as AyahBookmark | undefined;
    } else {
      // Return the first bookmark found for the ayah, regardless of color
      return bookmarks.find(
        b => b.type === 'ayah' && b.surah === surah && b.ayah === ayah
      ) as AyahBookmark | undefined;
    }
  };

  // Check if page is bookmarked
  const isPageBookmarked = (page: number) => {
    return bookmarks.some(b => b.type === 'page' && b.page === page);
  };

  return (
    <BookmarksContext.Provider value={{
      bookmarks,
      addAyahBookmark,
      addPageBookmark,
      removeBookmark,
      toggleAyahBookmark,
      togglePageBookmark, // Added togglePageBookmark
      isAyahBookmarked,
      isPageBookmarked
    }}>
      {children}
    </BookmarksContext.Provider>
  );
}

export function useBookmarks() {
  const ctx = useContext(BookmarksContext);
  if (!ctx) throw new Error('useBookmarks must be used within BookmarksProvider');
  return ctx;
}
