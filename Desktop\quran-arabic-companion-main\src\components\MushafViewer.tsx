import React, { useState, useEffect } from 'react';
import { useSwipeable } from 'react-swipeable';
import { Home, Moon, Sun, ChevronLeft, ChevronRight } from 'lucide-react';
import ayahData from '../data/ayahobject.json';
import PageSliderOverlay from './PageSliderOverlay';
import imageIndex from '../data/imageIndex.json';

interface MushafViewerProps {
  pageNumber: number;
  onBack: () => void;
  onPageChange: (newPage: number) => void;
  totalPages?: number;
}

interface Verse {
  id: number;
  text: string;
  verseNumber?: string;
}

const MushafViewer: React.FC<MushafViewerProps> = ({
  pageNumber,
  onBack,
  onPageChange,
  totalPages = 604
}) => {
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [verses, setVerses] = useState<Verse[]>([]);
  const [loading, setLoading] = useState(true);
  const [surahName, setSurahName] = useState('');
  const [showSlider, setShowSlider] = useState(false);

  // Convert to Arabic numbers
  const convertToArabicNumbers = (num: number): string => {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return num.toString().split('').map(digit => arabicNumbers[parseInt(digit)]).join('');
  };

  // Load dark mode from localStorage
  useEffect(() => {
    const savedDarkMode = localStorage.getItem('quran-dark-mode');
    if (savedDarkMode) {
      setIsDarkMode(savedDarkMode === 'true');
    }
  }, []);

  // Save dark mode to localStorage
  useEffect(() => {
    localStorage.setItem('quran-dark-mode', isDarkMode.toString());
  }, [isDarkMode]);

  // Load verses for current page
  useEffect(() => {
    const loadVerses = () => {
      setLoading(true);

      try {
        // Filter verses by page number from ayahData
        const pageVerses = ayahData
          .filter(ayah => ayah.page === pageNumber)
          .map(ayah => ({
            id: ayah.numberInSurah,
            text: ayah.uthmaniText || ayah.text,
            verseNumber: ayah.numberInSurahNative
          }));

        // Get surah name from first verse
        let currentSurahName = '';
        const firstVerse = ayahData.find(ayah => ayah.page === pageNumber);

        if (firstVerse && firstVerse.surah) {
          currentSurahName = firstVerse.surah.name;
        }

        setVerses(pageVerses);
        setSurahName(currentSurahName);
      } catch (error) {
        console.error('Error loading verses:', error);
        setVerses([]);
        setSurahName('');
      }

      setLoading(false);
    };

    loadVerses();
  }, [pageNumber]);

  // Swipe handlers
  const handlers = useSwipeable({
    onSwipedLeft: () => pageNumber < totalPages && onPageChange(pageNumber + 1),
    onSwipedRight: () => pageNumber > 1 && onPageChange(pageNumber - 1),
    trackMouse: true
  });

  // دالة لجلب صورة مصغرة للصفحة الحالية
  const getThumbnailUrl = (page: number) => {
    const entry = (imageIndex as any)[page];
    return entry ? entry.url : undefined;
  };

  // تعديل event ليكون single click بدلاً من double click
  useEffect(() => {
    const handleScreenClick = (e: MouseEvent) => {
      if ((e.target as HTMLElement).closest('.page-slider-overlay')) return;
      setShowSlider((prev) => !prev);
    };
    document.addEventListener('click', handleScreenClick);
    return () => document.removeEventListener('click', handleScreenClick);
  }, []);

  if (loading) {
    return (
      <div className={`min-h-screen flex items-center justify-center ${
        isDarkMode ? 'bg-gray-900 text-white' : 'bg-[#f5f3f0] text-gray-900'
      }`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-amber-500 mx-auto mb-4"></div>
          <p className="text-lg">جاري التحميل...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <div 
        {...handlers}
        className={`min-h-screen transition-colors duration-300 ${
          isDarkMode 
            ? 'bg-gray-900 text-white' 
            : 'bg-[#f5f3f0] text-gray-900'
        }`}
      >
        {/* Header */}
        <div className={`sticky top-0 z-10 px-4 py-3 border-b ${
          isDarkMode 
            ? 'bg-gray-800/90 border-gray-700 backdrop-blur-sm' 
            : 'bg-white/90 border-gray-300 backdrop-blur-sm'
        }`}>
          <div className="flex items-center justify-between">
            <button
              onClick={onBack}
              className={`p-2 rounded-full transition-colors ${
                isDarkMode 
                  ? 'hover:bg-gray-700 text-amber-400' 
                  : 'hover:bg-gray-100 text-gray-700'
              }`}
            >
              <Home size={24} />
            </button>

            <div className="text-center">
              <h1 className="text-lg font-bold">{surahName}</h1>
            </div>

            <button
              onClick={() => setIsDarkMode(!isDarkMode)}
              className={`p-2 rounded-full transition-colors ${
                isDarkMode 
                  ? 'hover:bg-gray-700 text-amber-400' 
                  : 'hover:bg-gray-100 text-gray-700'
              }`}
            >
              {isDarkMode ? <Sun size={24} /> : <Moon size={24} />}
            </button>
          </div>
        </div>

        {/* Content - Mushaf Style */}
        <div className="px-4 py-8">
          <div className="max-w-2xl mx-auto">
            {/* Page Container */}
            <div className={`${
              isDarkMode ? 'bg-gray-800 border-gray-600' : 'bg-white border-gray-400'
            } border-2 rounded-lg shadow-lg p-8 min-h-[600px]`}>
              
              {/* Surah Name Header */}
              {surahName && (
                <div className={`text-center mb-6 ${
                  isDarkMode ? 'border-gray-600' : 'border-gray-400'
                } border-2 rounded-md p-3 mx-4`}>
                  <h2 className="text-lg font-bold">{surahName}</h2>
                </div>
              )}
              
              {/* Verses Container */}
              <div className="text-right leading-loose text-xl" dir="rtl" style={{ fontFamily: 'Amiri, serif' }}>
                {verses.map((verse, index) => (
                  <span key={index} className="inline">
                    <span className="leading-loose">
                      {verse.text}
                    </span>
                    <span className={`inline-flex items-center justify-center w-5 h-5 rounded-full text-xs font-bold mx-1 ${
                      isDarkMode 
                        ? 'bg-white text-black' 
                        : 'bg-black text-white'
                    }`}>
                      {verse.verseNumber || convertToArabicNumbers(verse.id)}
                    </span>
                    {index < verses.length - 1 && ' '}
                  </span>
                ))}
              </div>
            </div>
            
            {/* Page Number - Bottom Center */}
            <div className="text-center mt-6">
              <span className={`text-xl font-bold ${
                isDarkMode ? 'text-white' : 'text-black'
              }`}>
                {convertToArabicNumbers(pageNumber)}
              </span>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <div className="fixed bottom-4 left-1/2 transform -translate-x-1/2">
          <div className={`flex items-center space-x-4 px-6 py-3 rounded-full ${
            isDarkMode ? 'bg-gray-800' : 'bg-white'
          } shadow-lg`}>
            <button
              onClick={() => pageNumber > 1 && onPageChange(pageNumber - 1)}
              disabled={pageNumber <= 1}
              className={`p-2 rounded-full transition-colors ${
                pageNumber <= 1 
                  ? 'opacity-50 cursor-not-allowed' 
                  : isDarkMode 
                    ? 'hover:bg-gray-700 text-amber-400' 
                    : 'hover:bg-gray-100 text-gray-700'
              }`}
            >
              <ChevronRight size={20} />
            </button>
            
            <span className="text-sm font-medium px-4">
              {convertToArabicNumbers(pageNumber)} / {convertToArabicNumbers(totalPages)}
            </span>
            
            <button
              onClick={() => pageNumber < totalPages && onPageChange(pageNumber + 1)}
              disabled={pageNumber >= totalPages}
              className={`p-2 rounded-full transition-colors ${
                pageNumber >= totalPages 
                  ? 'opacity-50 cursor-not-allowed' 
                  : isDarkMode 
                    ? 'hover:bg-gray-700 text-amber-400' 
                    : 'hover:bg-gray-100 text-gray-700'
              }`}
            >
              <ChevronLeft size={20} />
            </button>
          </div>
        </div>
      </div>
      {/* شريط التنقل السريع */}
      {showSlider && (
        <div className="page-slider-overlay">
          <PageSliderOverlay
            currentPage={pageNumber}
            totalPages={totalPages}
            surahName={surahName}
            juzNumber={1} // لا يوجد منطق الجزء هنا، يمكن تحسينه لاحقاً
            onPageChange={(p) => { setShowSlider(false); onPageChange(p); }}
            thumbnailUrl={getThumbnailUrl(pageNumber)}
            onClose={() => setShowSlider(false)}
          />
        </div>
      )}
    </>
  );
};

export default MushafViewer;
