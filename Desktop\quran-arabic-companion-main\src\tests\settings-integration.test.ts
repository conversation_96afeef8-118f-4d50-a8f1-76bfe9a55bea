/**
 * اختبارات تكامل الإعدادات مع مكونات التطبيق
 * Settings Integration Tests
 */

import { QuranSettings } from '../hooks/useQuranSettings';

/**
 * اختبار تطبيق حجم النص على العناصر
 */
export const testFontSizeApplication = (fontSize: number): boolean => {
  try {
    // إنشاء عنصر تجريبي
    const testElement = document.createElement('div');
    testElement.style.fontSize = `${fontSize}px`;
    testElement.style.fontFamily = 'UthmanicHafs, Amiri, serif';
    testElement.textContent = 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ';
    
    // إضافة العنصر للصفحة مؤقتاً
    document.body.appendChild(testElement);
    
    // قراءة الخصائص المطبقة
    const computedStyle = window.getComputedStyle(testElement);
    const appliedFontSize = parseInt(computedStyle.fontSize);
    
    // إزالة العنصر
    document.body.removeChild(testElement);
    
    // التحقق من تطبيق حجم النص الصحيح
    return appliedFontSize === fontSize;
    
  } catch (error) {
    console.error('خطأ في اختبار حجم النص:', error);
    return false;
  }
};

/**
 * اختبار تطبيق ألوان الثيم
 */
export const testThemeColorApplication = (colorTheme: string): boolean => {
  try {
    const themeColors = {
      red: '#ef4444',
      gray: '#6b7280',
      blue: '#3b82f6', 
      green: '#22c55e',
      dark: '#1f2937'
    };
    
    const expectedColor = themeColors[colorTheme as keyof typeof themeColors];
    if (!expectedColor) {
      console.error('لون ثيم غير صحيح:', colorTheme);
      return false;
    }
    
    // إنشاء عنصر تجريبي
    const testElement = document.createElement('div');
    testElement.style.color = expectedColor;
    testElement.textContent = 'نص تجريبي';
    
    // إضافة العنصر للصفحة مؤقتاً
    document.body.appendChild(testElement);
    
    // قراءة اللون المطبق
    const computedStyle = window.getComputedStyle(testElement);
    const appliedColor = computedStyle.color;
    
    // إزالة العنصر
    document.body.removeChild(testElement);
    
    // تحويل اللون المطبق إلى hex للمقارنة
    const rgbToHex = (rgb: string): string => {
      const result = rgb.match(/\d+/g);
      if (!result) return '';
      
      const r = parseInt(result[0]);
      const g = parseInt(result[1]);
      const b = parseInt(result[2]);
      
      return `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`;
    };
    
    const appliedHex = rgbToHex(appliedColor);
    
    return appliedHex.toLowerCase() === expectedColor.toLowerCase();
    
  } catch (error) {
    console.error('خطأ في اختبار لون الثيم:', error);
    return false;
  }
};

/**
 * اختبار تطبيق محاذاة النص
 */
export const testTextAlignmentApplication = (textAlignment: 'justified' | 'right'): boolean => {
  try {
    // إنشاء عنصر تجريبي
    const testElement = document.createElement('div');
    testElement.style.textAlign = textAlignment === 'justified' ? 'justify' : 'right';
    testElement.style.width = '200px';
    testElement.textContent = 'هذا نص تجريبي لاختبار المحاذاة';
    
    // إضافة العنصر للصفحة مؤقتاً
    document.body.appendChild(testElement);
    
    // قراءة المحاذاة المطبقة
    const computedStyle = window.getComputedStyle(testElement);
    const appliedAlignment = computedStyle.textAlign;
    
    // إزالة العنصر
    document.body.removeChild(testElement);
    
    // التحقق من المحاذاة
    const expectedAlignment = textAlignment === 'justified' ? 'justify' : 'right';
    return appliedAlignment === expectedAlignment;
    
  } catch (error) {
    console.error('خطأ في اختبار محاذاة النص:', error);
    return false;
  }
};

/**
 * اختبار شامل لتطبيق جميع الإعدادات
 */
export const testAllSettingsApplication = (settings: QuranSettings): boolean => {
  console.log('🧪 اختبار تطبيق جميع الإعدادات...');
  
  const tests = [
    {
      name: 'حجم النص',
      test: () => testFontSizeApplication(settings.fontSize)
    },
    {
      name: 'لون الثيم',
      test: () => testThemeColorApplication(settings.colorTheme)
    },
    {
      name: 'محاذاة النص',
      test: () => testTextAlignmentApplication(settings.textAlignment)
    }
  ];
  
  let passedTests = 0;
  
  for (const test of tests) {
    try {
      const result = test.test();
      if (result) {
        console.log(`✅ ${test.name}: نجح`);
        passedTests++;
      } else {
        console.log(`❌ ${test.name}: فشل`);
      }
    } catch (error) {
      console.log(`❌ ${test.name}: خطأ - ${error}`);
    }
  }
  
  const success = passedTests === tests.length;
  
  if (success) {
    console.log(`🎉 نجحت جميع اختبارات التطبيق! (${passedTests}/${tests.length})`);
  } else {
    console.log(`⚠️ فشل في ${tests.length - passedTests} من ${tests.length} اختبارات`);
  }
  
  return success;
};

/**
 * اختبار استجابة الإعدادات للتغييرات
 */
export const testSettingsResponsiveness = (): boolean => {
  try {
    console.log('🧪 اختبار استجابة الإعدادات للتغييرات...');
    
    // محاكاة تغيير الإعدادات
    const originalSettings = localStorage.getItem('quran-settings');
    
    // إعدادات تجريبية
    const testSettings: QuranSettings = {
      mushafType: 'connected',
      colorTheme: 'blue',
      displayMode: 'dark',
      fontSize: 24,
      numberFormat: 'arabic',
      textAlignment: 'justified',
      showSurahNames: true,
      showJuzInfo: true
    };
    
    // حفظ الإعدادات التجريبية
    localStorage.setItem('quran-settings', JSON.stringify(testSettings));
    
    // اختبار تطبيق الإعدادات الجديدة
    const result = testAllSettingsApplication(testSettings);
    
    // إعادة الإعدادات الأصلية
    if (originalSettings) {
      localStorage.setItem('quran-settings', originalSettings);
    } else {
      localStorage.removeItem('quran-settings');
    }
    
    return result;
    
  } catch (error) {
    console.error('خطأ في اختبار استجابة الإعدادات:', error);
    return false;
  }
};

/**
 * تشغيل جميع اختبارات التكامل
 */
export const runAllIntegrationTests = (): boolean => {
  console.log('🚀 بدء اختبارات التكامل الشاملة...');
  
  const tests = [
    testSettingsResponsiveness
  ];
  
  let passedTests = 0;
  
  for (const test of tests) {
    if (test()) {
      passedTests++;
    }
  }
  
  const success = passedTests === tests.length;
  
  if (success) {
    console.log(`🎉 نجحت جميع اختبارات التكامل! (${passedTests}/${tests.length})`);
  } else {
    console.log(`⚠️ فشل في ${tests.length - passedTests} من ${tests.length} اختبارات التكامل`);
  }
  
  return success;
};
