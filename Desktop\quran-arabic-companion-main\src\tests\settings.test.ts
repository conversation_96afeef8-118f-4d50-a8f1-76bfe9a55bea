/**
 * اختبارات شاملة لنظام الإعدادات
 * Tests for Quran Settings System
 */

import { QuranSettings } from '../hooks/useQuranSettings';

// محاكاة localStorage للاختبار
const mockLocalStorage = (() => {
  let store: { [key: string]: string } = {};
  
  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => {
      store[key] = value;
    },
    removeItem: (key: string) => {
      delete store[key];
    },
    clear: () => {
      store = {};
    }
  };
})();

// استبدال localStorage الحقيقي بالمحاكاة
Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage
});

const STORAGE_KEY = 'quran-settings';

const DEFAULT_SETTINGS: QuranSettings = {
  mushafType: 'connected',
  colorTheme: 'green',
  displayMode: 'dark',
  fontSize: 20,
  numberFormat: 'arabic',
  textAlignment: 'justified',
  showSurahNames: true,
  showJuzInfo: true,
};

/**
 * اختبار حفظ الإعدادات في localStorage
 */
export const testLocalStorageSave = (): boolean => {
  console.log('🧪 اختبار حفظ الإعدادات في localStorage...');
  
  try {
    // مسح localStorage
    localStorage.clear();
    
    // حفظ إعدادات تجريبية
    const testSettings: QuranSettings = {
      ...DEFAULT_SETTINGS,
      fontSize: 24,
      colorTheme: 'blue',
      displayMode: 'light'
    };
    
    localStorage.setItem(STORAGE_KEY, JSON.stringify(testSettings));
    
    // استرجاع الإعدادات
    const savedData = localStorage.getItem(STORAGE_KEY);
    if (!savedData) {
      console.error('❌ فشل في حفظ الإعدادات');
      return false;
    }
    
    const parsedSettings = JSON.parse(savedData);
    
    // التحقق من صحة البيانات المحفوظة
    if (parsedSettings.fontSize !== 24 || 
        parsedSettings.colorTheme !== 'blue' || 
        parsedSettings.displayMode !== 'light') {
      console.error('❌ البيانات المحفوظة غير صحيحة');
      return false;
    }
    
    console.log('✅ تم حفظ الإعدادات بنجاح');
    return true;
    
  } catch (error) {
    console.error('❌ خطأ في اختبار localStorage:', error);
    return false;
  }
};

/**
 * اختبار استرجاع الإعدادات من localStorage
 */
export const testLocalStorageLoad = (): boolean => {
  console.log('🧪 اختبار استرجاع الإعدادات من localStorage...');
  
  try {
    // حفظ إعدادات تجريبية أولاً
    const testSettings: QuranSettings = {
      ...DEFAULT_SETTINGS,
      fontSize: 28,
      colorTheme: 'red',
      numberFormat: 'english'
    };
    
    localStorage.setItem(STORAGE_KEY, JSON.stringify(testSettings));
    
    // استرجاع الإعدادات
    const savedData = localStorage.getItem(STORAGE_KEY);
    if (!savedData) {
      console.error('❌ لا توجد إعدادات محفوظة');
      return false;
    }
    
    const loadedSettings = JSON.parse(savedData);
    
    // التحقق من صحة الاسترجاع
    if (loadedSettings.fontSize !== 28 || 
        loadedSettings.colorTheme !== 'red' || 
        loadedSettings.numberFormat !== 'english') {
      console.error('❌ فشل في استرجاع الإعدادات الصحيحة');
      return false;
    }
    
    console.log('✅ تم استرجاع الإعدادات بنجاح');
    return true;
    
  } catch (error) {
    console.error('❌ خطأ في اختبار استرجاع localStorage:', error);
    return false;
  }
};

/**
 * اختبار التعامل مع الإعدادات الافتراضية
 */
export const testDefaultSettings = (): boolean => {
  console.log('🧪 اختبار الإعدادات الافتراضية...');
  
  try {
    // مسح localStorage
    localStorage.clear();
    
    // محاولة استرجاع إعدادات غير موجودة
    const savedData = localStorage.getItem(STORAGE_KEY);
    
    if (savedData !== null) {
      console.error('❌ يجب أن تكون الإعدادات فارغة');
      return false;
    }
    
    // التحقق من أن الإعدادات الافتراضية صحيحة
    if (DEFAULT_SETTINGS.fontSize !== 20 || 
        DEFAULT_SETTINGS.colorTheme !== 'green' || 
        DEFAULT_SETTINGS.displayMode !== 'dark') {
      console.error('❌ الإعدادات الافتراضية غير صحيحة');
      return false;
    }
    
    console.log('✅ الإعدادات الافتراضية صحيحة');
    return true;
    
  } catch (error) {
    console.error('❌ خطأ في اختبار الإعدادات الافتراضية:', error);
    return false;
  }
};

/**
 * اختبار شامل لجميع وظائف localStorage
 */
export const runAllLocalStorageTests = (): boolean => {
  console.log('🚀 بدء الاختبارات الشاملة لـ localStorage...');
  
  const tests = [
    testDefaultSettings,
    testLocalStorageSave,
    testLocalStorageLoad
  ];
  
  let passedTests = 0;
  
  for (const test of tests) {
    if (test()) {
      passedTests++;
    }
  }
  
  const success = passedTests === tests.length;
  
  if (success) {
    console.log(`🎉 نجحت جميع الاختبارات! (${passedTests}/${tests.length})`);
  } else {
    console.log(`⚠️ فشل في ${tests.length - passedTests} من ${tests.length} اختبارات`);
  }
  
  return success;
};

/**
 * اختبار تحديث إعداد واحد
 */
export const testSingleSettingUpdate = (): boolean => {
  console.log('🧪 اختبار تحديث إعداد واحد...');
  
  try {
    // حفظ إعدادات أولية
    localStorage.setItem(STORAGE_KEY, JSON.stringify(DEFAULT_SETTINGS));
    
    // تحديث إعداد واحد
    const updatedSettings = {
      ...DEFAULT_SETTINGS,
      fontSize: 32
    };
    
    localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedSettings));
    
    // التحقق من التحديث
    const savedData = localStorage.getItem(STORAGE_KEY);
    const parsedSettings = JSON.parse(savedData!);
    
    if (parsedSettings.fontSize !== 32) {
      console.error('❌ فشل في تحديث حجم النص');
      return false;
    }
    
    // التحقق من أن باقي الإعدادات لم تتغير
    if (parsedSettings.colorTheme !== DEFAULT_SETTINGS.colorTheme ||
        parsedSettings.displayMode !== DEFAULT_SETTINGS.displayMode) {
      console.error('❌ تغيرت إعدادات أخرى بالخطأ');
      return false;
    }
    
    console.log('✅ تم تحديث الإعداد بنجاح');
    return true;
    
  } catch (error) {
    console.error('❌ خطأ في اختبار تحديث الإعداد:', error);
    return false;
  }
};
