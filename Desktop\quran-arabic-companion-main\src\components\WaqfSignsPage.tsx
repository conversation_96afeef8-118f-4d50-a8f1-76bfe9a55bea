import React from "react";

const WAQF_SIGNS = [
  { sign: "قلى", desc: "الوقف أولى مع جواز الوصل" },
  { sign: "صلى", desc: "الوصل أولى مع جواز الوقف" },
  { sign: "ج", desc: "علامة تفيد جواز الوقف" },
  { sign: "۩", desc: "للدلالة على موضع السجود أما الكلمة التي يسجد بعد قراءتها فقد وضع عندها خط" },
  { sign: "••", desc: "لتفيد جواز الوقف بأحد الموضعين وليس في كليهما" },
  { sign: "م", desc: "للدلالة على وجوب الإقلاب" },
  { sign: "ا", desc: "للدلالة على النطق بالحروف المتروكة" },
  { sign: "س", desc: "للدلالة على النطق بالسين بدلا عن الصاد وإذا وضعت بالأسفل فالنطق بالصاد أشهر" },
  { sign: "مـ", desc: "للدلالة على لزوم العد" },
  { sign: "✽", desc: "للدلالة على بداية الأجزاء والأحزاب وأنصافها وأرباعها" },
];

const WaqfSignsPage = ({ onBack }: { onBack: () => void }) => {
  React.useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <div className="flex items-center p-4 border-b border-gray-800">
        <button onClick={onBack} className="text-white text-lg font-bold mr-2">←</button>
        <h2 className="text-xl font-bold flex-1 text-center">علامات الوقف والوصل</h2>
      </div>
      <div className="p-4 space-y-4 pb-24">
        {WAQF_SIGNS.map((item, idx) => (
          <div key={idx} className="bg-gray-800 rounded-2xl p-4 text-right text-lg leading-loose shadow-xl border border-gray-700 flex items-center gap-4">
            <span className="font-bold text-green-400 text-2xl min-w-[2.5rem] text-center">{item.sign}</span>
            <span className="mx-2 text-gray-400">—</span>
            <span>{item.desc}</span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default WaqfSignsPage; 