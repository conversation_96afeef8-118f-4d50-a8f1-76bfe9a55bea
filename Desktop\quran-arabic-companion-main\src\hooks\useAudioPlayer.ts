import { useState, useEffect, useRef, useCallback } from 'react';
import surahData from '../data/surah.json';

export interface AudioSettings {
  // إعدادات المقطع
  fromSurah: string;
  fromPage: number;
  fromAyah: number;
  toSurah: string;
  toPage: number;
  toAyah: number;
  
  // إعدادات القارئ
  selectedReciter: {
    id: string;
    name: string;
    folder: string;
    bitrate?: string;
    style?: string;
    country?: string;
    recitation?: string;
    recitationName?: string;
    image?: string;
  };
  
  // إعدادات التكرار
  repeatSection: {
    count: number;
    unlimited: boolean;
  };
  
  repeatAyah: {
    count: number;
    unlimited: boolean;
  };
  
  // إعدادات إضافية
  autoComplete: boolean;
  autoScroll: boolean;
  playbackSpeed: number;
  
  // قائمة التشغيل
  currentPlaylist?: string;
}

export interface AudioState {
  isPlaying: boolean;
  currentAyah: number;
  currentSurah: string;
  currentPage: number;
  progress: number;
  duration: number;
  volume: number;
  isLoading: boolean;
  error: string | null;
}

const DEFAULT_SETTINGS: AudioSettings = {
  fromSurah: 'الفاتحة',
  fromPage: 1,
  fromAyah: 1,
  toSurah: 'الفاتحة',
  toPage: 1,
  toAyah: 7,
  selectedReciter: {
    id: 'yasser_dossary',
    name: 'ياسر الدوسري',
    folder: 'Yasser_Ad-Dussary_128kbps',
    bitrate: '128kbps',
    style: 'مرتل',
    country: 'السعودية',
    recitation: 'hafs',
    recitationName: 'حفص عن عاصم'
  },
  repeatSection: {
    count: 1,
    unlimited: false
  },
  repeatAyah: {
    count: 1,
    unlimited: false
  },
  autoComplete: true,
  autoScroll: true, // الافتراضي الآن مفعّل
  playbackSpeed: 1.0,
  currentPlaylist: undefined
};

const DEFAULT_STATE: AudioState = {
  isPlaying: false,
  currentAyah: 1,
  currentSurah: 'الفاتحة',
  currentPage: 1,
  progress: 0,
  duration: 0,
  volume: 1,
  isLoading: false,
  error: null
};

const STORAGE_KEY = 'quran-audio-settings';

// قائمة القراء مقسمة حسب الروايات - شاملة من EveryAyah
export const RECITATION_TYPES = {
  'hafs': {
    id: 'hafs',
    name: 'حفص عن عاصم',
    nameEn: 'Hafs from Asim',
    description: 'الرواية الأكثر انتشاراً في العالم الإسلامي',
    isDefault: true,
    reciters: [
      // القراء المصريون - التراث الكلاسيكي
      { id: 'abdul_basit_murattal_64', name: 'عبد الباسط عبد الصمد (مرتل)', folder: 'Abdul_Basit_Murattal_64kbps', bitrate: '64kbps', style: 'مرتل', country: 'مصر' },
      { id: 'abdul_basit_murattal_192', name: 'عبد الباسط عبد الصمد (مرتل - جودة عالية)', folder: 'Abdul_Basit_Murattal_192kbps', bitrate: '192kbps', style: 'مرتل', country: 'مصر' },
      { id: 'abdul_basit_mujawwad', name: 'عبد الباسط عبد الصمد (مجود)', folder: 'Abdul_Basit_Mujawwad_128kbps', bitrate: '128kbps', style: 'مجود', country: 'مصر' },
      { id: 'husary_64', name: 'محمود خليل الحصري', folder: 'Husary_64kbps', bitrate: '64kbps', style: 'مرتل', country: 'مصر' },
      { id: 'husary_128', name: 'محمود خليل الحصري', folder: 'Husary_128kbps', bitrate: '128kbps', style: 'مرتل', country: 'مصر' },
      { id: 'husary_mujawwad_64', name: 'محمود خليل الحصري (مجود)', folder: 'Husary_Mujawwad_64kbps', bitrate: '64kbps', style: 'مجود', country: 'مصر' },
      { id: 'husary_mujawwad_128', name: 'محمود خليل الحصري (مجود)', folder: 'Husary_128kbps_Mujawwad', bitrate: '128kbps', style: 'مجود', country: 'مصر' },
      { id: 'husary_muallim', name: 'محمود خليل الحصري (معلم)', folder: 'Husary_Muallim_128kbps', bitrate: '128kbps', style: 'تعليمي', country: 'مصر' },
      // محمد صديق المنشاوي - كل الجودات والأساليب مرتبة
      { id: 'menshawi_16', name: 'محمد صديق المنشاوي (جودة منخفضة جداً)', folder: 'Menshawi_16kbps', bitrate: '16kbps', style: 'مرتل', country: 'مصر' },
      { id: 'menshawi_32', name: 'محمد صديق المنشاوي (جودة منخفضة)', folder: 'Menshawi_32kbps', bitrate: '32kbps', style: 'مرتل', country: 'مصر' },
      { id: 'minshawy_mujawwad_64', name: 'محمد صديق المنشاوي (مجود)', folder: 'Minshawy_Mujawwad_64kbps', bitrate: '64kbps', style: 'مجود', country: 'مصر' },
      { id: 'minshawy_murattal', name: 'محمد صديق المنشاوي (مرتل)', folder: 'Minshawy_Murattal_128kbps', bitrate: '128kbps', style: 'مرتل', country: 'مصر' },
      { id: 'minshawy_teacher', name: 'محمد صديق المنشاوي (معلم)', folder: 'Minshawy_Teacher_128kbps', bitrate: '128kbps', style: 'تعليمي', country: 'مصر' },
      { id: 'minshawy_mujawwad_192', name: 'محمد صديق المنشاوي (مجود - جودة عالية)', folder: 'Minshawy_Mujawwad_192kbps', bitrate: '192kbps', style: 'مجود', country: 'مصر' },
      { id: 'mohammad_tablaway_64', name: 'محمد الطبلاوي', folder: 'Mohammad_al_Tablaway_64kbps', bitrate: '64kbps', style: 'مرتل', country: 'مصر' },
      { id: 'mohammad_tablaway_128', name: 'محمد الطبلاوي', folder: 'Mohammad_al_Tablaway_128kbps', bitrate: '128kbps', style: 'مرتل', country: 'مصر' },
      { id: 'mustafa_ismail', name: 'مصطفى إسماعيل', folder: 'Mustafa_Ismail_48kbps', bitrate: '48kbps', style: 'مرتل', country: 'مصر' },
      { id: 'ibrahim_akhdar_32', name: 'إبراهيم الأخضر', folder: 'Ibrahim_Akhdar_32kbps', bitrate: '32kbps', style: 'مرتل', country: 'مصر' },
      // تم حذف إبراهيم الأخضر (64kbps) لأن الملفات غير موجودة على الخادم
      { id: 'hani_rifai_64', name: 'هاني الرفاعي', folder: 'Hani_Rifai_64kbps', bitrate: '64kbps', style: 'مرتل', country: 'مصر' },
      { id: 'hani_rifai_192', name: 'هاني الرفاعي (جودة عالية)', folder: 'Hani_Rifai_192kbps', bitrate: '192kbps', style: 'مرتل', country: 'مصر' },
      { id: 'muhammad_jibreel_64', name: 'محمد جبريل', folder: 'Muhammad_Jibreel_64kbps', bitrate: '64kbps', style: 'مرتل', country: 'مصر' },
      { id: 'muhammad_jibreel_128', name: 'محمد جبريل', folder: 'Muhammad_Jibreel_128kbps', bitrate: '128kbps', style: 'مرتل', country: 'مصر' },
      { id: 'ahmed_neana', name: 'أحمد نعينع', folder: 'Ahmed_Neana_128kbps', bitrate: '128kbps', style: 'مرتل', country: 'مصر' },
      { id: 'muhammad_abdulkareem', name: 'محمد عبد الكريم', folder: 'Muhammad_AbdulKareem_128kbps', bitrate: '128kbps', style: 'مرتل', country: 'مصر' },
      { id: 'mahmoud_banna', name: 'محمود علي البنا', folder: 'mahmoud_ali_al_banna_32kbps', bitrate: '32kbps', style: 'مرتل', country: 'مصر' },
      { id: 'ali_hajjaj_suesy', name: 'علي حجاج السويسي', folder: 'Ali_Hajjaj_AlSuesy_128kbps', bitrate: '128kbps', style: 'مرتل', country: 'مصر' },
      { id: 'sahl_yassin', name: 'سهل ياسين', folder: 'Sahl_Yassin_128kbps', bitrate: '128kbps', style: 'مرتل', country: 'مصر' },
      { id: 'yaser_salamah', name: 'ياسر سلامة', folder: 'Yaser_Salamah_128kbps', bitrate: '128kbps', style: 'مرتل', country: 'مصر' },

      // القراء السعوديون - الأكثر تمثيلاً
      { id: 'abdurrahman_sudais_64', name: 'عبد الرحمن السديس', folder: 'Abdurrahmaan_As-Sudais_64kbps', bitrate: '64kbps', style: 'مرتل', country: 'السعودية' },
      { id: 'abdurrahman_sudais_192', name: 'عبد الرحمن السديس (جودة عالية)', folder: 'Abdurrahmaan_As-Sudais_192kbps', bitrate: '192kbps', style: 'مرتل', country: 'السعودية' },
      { id: 'saood_shuraym_64', name: 'سعود الشريم', folder: 'Saood_ash-Shuraym_64kbps', bitrate: '64kbps', style: 'مرتل', country: 'السعودية' },
      { id: 'saood_shuraym_128', name: 'سعود الشريم', folder: 'Saood_ash-Shuraym_128kbps', bitrate: '128kbps', style: 'مرتل', country: 'السعودية' },
      { id: 'maher_muaiqly_64', name: 'ماهر المعيقلي', folder: 'Maher_AlMuaiqly_64kbps', bitrate: '64kbps', style: 'مرتل', country: 'السعودية' },
      { id: 'maher_muaiqly_128', name: 'ماهر المعيقلي', folder: 'MaherAlMuaiqly128kbps', bitrate: '128kbps', style: 'مرتل', country: 'السعودية' },
      { id: 'yasser_dossary', name: 'ياسر الدوسري', folder: 'Yasser_Ad-Dussary_128kbps', bitrate: '128kbps', style: 'مرتل', country: 'السعودية' },
      { id: 'abdullah_basfar_32', name: 'عبد الله بصفر', folder: 'Abdullah_Basfar_32kbps', bitrate: '32kbps', style: 'مرتل', country: 'السعودية' },
      { id: 'abdullah_basfar_64', name: 'عبد الله بصفر', folder: 'Abdullah_Basfar_64kbps', bitrate: '64kbps', style: 'مرتل', country: 'السعودية' },
      { id: 'abdullah_basfar_192', name: 'عبد الله بصفر (جودة عالية)', folder: 'Abdullah_Basfar_192kbps', bitrate: '192kbps', style: 'مرتل', country: 'السعودية' },
      { id: 'ahmed_ajamy_64', name: 'أحمد بن علي العجمي', folder: 'Ahmed_ibn_Ali_al-Ajamy_64kbps_QuranExplorer.Com', bitrate: '64kbps', style: 'مرتل', country: 'السعودية' },
      { id: 'ahmed_ajamy_128', name: 'أحمد بن علي العجمي', folder: 'Ahmed_ibn_Ali_al-Ajamy_128kbps_ketaballah.net', bitrate: '128kbps', style: 'مرتل', country: 'السعودية' },
      { id: 'ghamdi', name: 'سعد الغامدي', folder: 'Ghamadi_40kbps', bitrate: '40kbps', style: 'مرتل', country: 'السعودية' },
      { id: 'hudhaify_32', name: 'علي عبد الرحمن الحذيفي', folder: 'Hudhaify_32kbps', bitrate: '32kbps', style: 'مرتل', country: 'السعودية' },
      { id: 'hudhaify_64', name: 'علي عبد الرحمن الحذيفي', folder: 'Hudhaify_64kbps', bitrate: '64kbps', style: 'مرتل', country: 'السعودية' },
      { id: 'hudhaify_128', name: 'علي عبد الرحمن الحذيفي', folder: 'Hudhaify_128kbps', bitrate: '128kbps', style: 'مرتل', country: 'السعودية' },
      { id: 'nasser_alqatami', name: 'ناصر القطامي', folder: 'Nasser_Alqatami_128kbps', bitrate: '128kbps', style: 'مرتل', country: 'السعودية' },
      { id: 'abdullah_juhaynee', name: 'عبد الله عواد الجهني', folder: 'Abdullaah_3awwaad_Al-Juhaynee_128kbps', bitrate: '128kbps', style: 'مرتل', country: 'السعودية' },
      { id: 'salah_budair', name: 'صلاح البدير', folder: 'Salah_Al_Budair_128kbps', bitrate: '128kbps', style: 'مرتل', country: 'السعودية' },
      { id: 'abdullah_matroud', name: 'عبد الله مطرود', folder: 'Abdullah_Matroud_128kbps', bitrate: '128kbps', style: 'مرتل', country: 'السعودية' },
      { id: 'khalid_qahtanee', name: 'خالد عبد الله القحطاني', folder: 'Khaalid_Abdullaah_al-Qahtaanee_192kbps', bitrate: '192kbps', style: 'مرتل', country: 'السعودية' },
      { id: 'ali_jaber', name: 'علي جابر', folder: 'Ali_Jaber_64kbps', bitrate: '64kbps', style: 'مرتل', country: 'السعودية' },

      // القراء الكويتيون
      { id: 'alafasy_64', name: 'مشاري راشد العفاسي', folder: 'Alafasy_64kbps', bitrate: '64kbps', style: 'مرتل', country: 'الكويت' },
      { id: 'alafasy_128', name: 'مشاري راشد العفاسي', folder: 'Alafasy_128kbps', bitrate: '128kbps', style: 'مرتل', country: 'الكويت' },
      { id: 'fares_abbad', name: 'فارس عباد', folder: 'Fares_Abbad_64kbps', bitrate: '64kbps', style: 'مرتل', country: 'الكويت' },

      // القراء الإماراتيون
      { id: 'salah_bukhatir', name: 'صلاح عبد الرحمن بخاطر', folder: 'Salaah_AbdulRahman_Bukhatir_128kbps', bitrate: '128kbps', style: 'مرتل', country: 'الإمارات' },
      { id: 'khalefa_tunaiji', name: 'خليفة الطنيجي', folder: 'khalefa_al_tunaiji_64kbps', bitrate: '64kbps', style: 'مرتل', country: 'الإمارات' },

      // القراء اليمنيون
      { id: 'abu_bakr_shatri_64', name: 'أبو بكر الشاطري', folder: 'Abu_Bakr_Ash-Shaatree_64kbps', bitrate: '64kbps', style: 'مرتل', country: 'اليمن' },
      { id: 'abu_bakr_shatri_128', name: 'أبو بكر الشاطري', folder: 'Abu_Bakr_Ash-Shaatree_128kbps', bitrate: '128kbps', style: 'مرتل', country: 'اليمن' },

      // القراء العراقيون
      { id: 'muhsin_qasim', name: 'محسن القاسمي', folder: 'Muhsin_Al_Qasim_192kbps', bitrate: '192kbps', style: 'مرتل', country: 'العراق' },
      { id: 'akram_alalaqimy', name: 'أكرم العلاقمي', folder: 'Akram_AlAlaqimy_128kbps', bitrate: '128kbps', style: 'مرتل', country: 'العراق' },

      // القراء السوريون
      { id: 'ayman_sowaid', name: 'أيمن سويد', folder: 'Ayman_Sowaid_64kbps', bitrate: '64kbps', style: 'تعليمي', country: 'سوريا' },
      { id: 'nabil_rifai', name: 'نبيل الرفاعي', folder: 'Nabil_Rifa3i_48kbps', bitrate: '48kbps', style: 'مرتل', country: 'سوريا' },

      // القراء المغاربة
      { id: 'aziz_alili', name: 'عزيز عليلي', folder: 'aziz_alili_128kbps', bitrate: '128kbps', style: 'مرتل', country: 'المغرب' },

      // القراء الباكستانيون
      { id: 'muhammad_ayyoub_32', name: 'محمد أيوب', folder: 'Muhammad_Ayyoub_32kbps', bitrate: '32kbps', style: 'مرتل', country: 'باكستان' },
      { id: 'muhammad_ayyoub_64', name: 'محمد أيوب', folder: 'Muhammad_Ayyoub_64kbps', bitrate: '64kbps', style: 'مرتل', country: 'باكستان' },
      { id: 'muhammad_ayyoub_128', name: 'محمد أيوب', folder: 'Muhammad_Ayyoub_128kbps', bitrate: '128kbps', style: 'مرتل', country: 'باكستان' },

      // القراء الإيرانيون
      { id: 'karim_mansoori', name: 'كريم منصوري', folder: 'Karim_Mansoori_40kbps', bitrate: '40kbps', style: 'مرتل', country: 'إيران' },


    ]
  },
  'warsh': {
    id: 'warsh',
    name: 'ورش عن نافع',
    nameEn: 'Warsh from Nafi',
    description: 'الرواية المنتشرة في شمال أفريقيا والمغرب العربي',
    isDefault: false,
    reciters: [
      { id: 'warsh_ibrahim_dosary', name: 'إبراهيم الدوسري (ورش)', folder: 'warsh/warsh_ibrahim_aldosary_128kbps', bitrate: '128kbps', style: 'مرتل', country: 'السعودية' },
      { id: 'warsh_yassin_jazaery', name: 'ياسين الجزائري (ورش)', folder: 'warsh/warsh_yassin_al_jazaery_64kbps', bitrate: '64kbps', style: 'مرتل', country: 'الجزائر' },
      { id: 'warsh_abdul_basit', name: 'عبد الباسط عبد الصمد (ورش)', folder: 'warsh/warsh_Abdul_Basit_128kbps', bitrate: '128kbps', style: 'مرتل', country: 'مصر' }
    ]
  }
};

// دالة مساعدة للحصول على قائمة مسطحة لكل القراء
export const getAllReciters = () => {
  const allReciters: any[] = [];
  Object.values(RECITATION_TYPES).forEach(recitation => {
    recitation.reciters.forEach(reciter => {
      allReciters.push({
        ...reciter,
        recitation: recitation.id,
        recitationName: recitation.name
      });
    });
  });
  return allReciters;
};

// للتوافق مع الكود القديم
export const RECITERS = getAllReciters();

// دالة لتحويل اسم السورة إلى رقمها (1-114)
const SURAH_NAME_TO_NUMBER: Record<string, number> = {
  'الفاتحة': 1, 'البقرة': 2, 'آل عمران': 3, 'النساء': 4, 'المائدة': 5, 'الأنعام': 6, 'الأعراف': 7, 'الأنفال': 8, 'التوبة': 9, 'يونس': 10, 'هود': 11, 'يوسف': 12, 'الرعد': 13, 'إبراهيم': 14, 'الحجر': 15, 'النحل': 16, 'الإسراء': 17, 'الكهف': 18, 'مريم': 19, 'طه': 20, 'الأنبياء': 21, 'الحج': 22, 'المؤمنون': 23, 'النور': 24, 'الفرقان': 25, 'الشعراء': 26, 'النمل': 27, 'القصص': 28, 'العنكبوت': 29, 'الروم': 30, 'لقمان': 31, 'السجدة': 32, 'الأحزاب': 33, 'سبأ': 34, 'فاطر': 35, 'يس': 36, 'الصافات': 37, 'ص': 38, 'الزمر': 39, 'غافر': 40, 'فصلت': 41, 'الشورى': 42, 'الزخرف': 43, 'الدخان': 44, 'الجاثية': 45, 'الأحقاف': 46, 'محمد': 47, 'الفتح': 48, 'الحجرات': 49, 'ق': 50, 'الذاريات': 51, 'الطور': 52, 'النجم': 53, 'القمر': 54, 'الرحمن': 55, 'الواقعة': 56, 'الحديد': 57, 'المجادلة': 58, 'الحشر': 59, 'الممتحنة': 60, 'الصف': 61, 'الجمعة': 62, 'المنافقون': 63, 'التغابن': 64, 'الطلاق': 65, 'التحريم': 66, 'الملك': 67, 'القلم': 68, 'الحاقة': 69, 'المعارج': 70, 'نوح': 71, 'الجن': 72, 'المزمل': 73, 'المدثر': 74, 'القيامة': 75, 'الانسان': 76, 'المرسلات': 77, 'النبأ': 78, 'النازعات': 79, 'عبس': 80, 'التكوير': 81, 'الانفطار': 82, 'المطففين': 83, 'الانشقاق': 84, 'البروج': 85, 'الطارق': 86, 'الأعلى': 87, 'الغاشية': 88, 'الفجر': 89, 'البلد': 90, 'الشمس': 91, 'الليل': 92, 'الضحى': 93, 'الشرح': 94, 'التين': 95, 'العلق': 96, 'القدر': 97, 'البينة': 98, 'الزلزلة': 99, 'العاديات': 100, 'القارعة': 101, 'التكاثر': 102, 'العصر': 103, 'الهمزة': 104, 'الفيل': 105, 'قريش': 106, 'الماعون': 107, 'الكوثر': 108, 'الكافرون': 109, 'النصر': 110, 'المسد': 111, 'الإخلاص': 112, 'الفلق': 113, 'الناس': 114
};
function surahNumberFromName(surah: string): number {
  return SURAH_NAME_TO_NUMBER[surah] || 1;
}

function surahNameFromNumber(surahNum: number): string {
  const surahName = Object.keys(SURAH_NAME_TO_NUMBER).find(key => SURAH_NAME_TO_NUMBER[key] === surahNum);
  return surahName || 'القلم'; // Default to 'القلم' if not found
}

// تحديث دالة getSurahAyahCount
function getSurahAyahCount(surah: string): number {
  const surahObj = surahData.find(s => s.titleAr === surah || s.title === surah);
  const count = surahObj ? surahObj.count : 7; // الافتراضي 7 (للفاتحة)
  console.log('📊 عدد آيات السورة:', surah, '=', count);
  return count;
}

function getPageFromSurahAyah(surah: string, ayah: number): number {
  // استخدام نفس المنطق المستخدم في QuranPageViewer
  try {
    // تحويل اسم السورة إلى رقم
    const surahNumber = surahNumberFromName(surah);

    // البحث في بيانات الآيات للعثور على الصفحة
    // نحتاج لاستيراد ayahData هنا، لكن للآن سنستخدم تقدير تقريبي

    // خريطة تقريبية لصفحات بداية السور (يمكن تحسينها لاحقاً)
    const SURAH_START_PAGES: Record<number, number> = {
      1: 1,   // الفاتحة
      2: 2,   // البقرة
      3: 50,  // آل عمران
      4: 77,  // النساء
      5: 106, // المائدة
      6: 128, // الأنعام
      7: 151, // الأعراف
      8: 177, // الأنفال
      9: 187, // التوبة
      10: 208, // يونس
      // ... يمكن إضافة باقي السور
    };

    // إرجاع الصفحة التقريبية للسورة
    return SURAH_START_PAGES[surahNumber] || 1;
  } catch (error) {
    console.error('خطأ في تحديد الصفحة:', error);
    return 1;
  }
}

// مرجع عالمي لضمان وجود مشغل صوت واحد فقط
let globalAudioInstance: HTMLAudioElement | null = null;

export const useAudioPlayer = () => {
  const [settings, setSettings] = useState<AudioSettings>(DEFAULT_SETTINGS);
  const [state, setState] = useState<AudioState>(DEFAULT_STATE);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const nextAudioRef = useRef<HTMLAudioElement | null>(null); // مشغل للآية التالية
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const ayahRepeatCounterRef = useRef(0);
  const sectionRepeatCounterRef = useRef(0);

  // أبقِ فقط useEffect الأول الذي يحمل الإعدادات عند mount:
  useEffect(() => {
    try {
      const savedSettings = localStorage.getItem(STORAGE_KEY);
      if (savedSettings) {
        const parsed = JSON.parse(savedSettings);
        // إذا كان selectedReciter موجودًا، دمج كل خصائص القارئ من RECITERS
        let selectedReciter = parsed.selectedReciter;
        if (selectedReciter) {
          const reciterObj = RECITERS.find(r => r.id === selectedReciter.id || r.name === selectedReciter.name || r.folder === selectedReciter.folder);
          if (reciterObj) {
            selectedReciter = { ...reciterObj };
          }
        }
        setSettings({ ...DEFAULT_SETTINGS, ...parsed, selectedReciter });
      }
    } catch (error) {
      console.error('Error loading audio settings:', error);
    }
  }, []);

  // حفظ الإعدادات في localStorage
  const saveSettings = useCallback((newSettings: Partial<AudioSettings>) => {
    console.log('💾 saveSettings تم استدعاؤها مع:', newSettings);
    setSettings(prevSettings => {
      console.log('📋 الإعدادات السابقة:', {
        fromSurah: prevSettings.fromSurah,
        fromAyah: prevSettings.fromAyah,
        toSurah: prevSettings.toSurah,
        toAyah: prevSettings.toAyah
      });
      const updatedSettings = { ...prevSettings, ...newSettings };
      console.log('📋 الإعدادات المحدثة:', {
        fromSurah: updatedSettings.fromSurah,
        fromAyah: updatedSettings.fromAyah,
        toSurah: updatedSettings.toSurah,
        toAyah: updatedSettings.toAyah
      });
      try {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedSettings));
        console.log('✅ تم حفظ الإعدادات في localStorage');
      } catch (error) {
        console.error('Error saving audio settings:', error);
      }
      return updatedSettings;
    });
  }, []);

  // إنشاء عنصر الصوت (فقط في المتصفح) مع ضمان وجود مشغل واحد فقط
  useEffect(() => {
    if (typeof window === 'undefined') return;

    try {
      // إيقاف أي صوت آخر قيد التشغيل
      if (globalAudioInstance && !globalAudioInstance.paused) {
        globalAudioInstance.pause();
        console.log('🛑 تم إيقاف مشغل صوت آخر');
      }

      if (!audioRef.current) {
        audioRef.current = new Audio();
        audioRef.current.preload = 'metadata';
        globalAudioInstance = audioRef.current; // تعيين المرجع العالمي
        console.log('🎵 تم إنشاء مشغل صوت جديد');
      }

      const audio = audioRef.current;

      const handleLoadStart = () => {
        console.log('🔄 بدء تحميل الصوت');
        setState(prev => ({ ...prev, isLoading: true, error: null }));
      };
      const handleCanPlay = () => {
        console.log('✅ الصوت جاهز للتشغيل');
        setState(prev => ({ ...prev, isLoading: false }));
      };
      const handleError = (e: any) => {
        console.error('❌ خطأ في تحميل الصوت:', e);
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: 'خطأ في تحميل الملف الصوتي'
        }));
      };
      const handleLoadedMetadata = () => {
        console.log('📊 تم تحميل معلومات الصوت، المدة:', audio.duration);
        setState(prev => ({ ...prev, duration: audio.duration || 0 }));
      };
      const handleTimeUpdate = () => {
        setState(prev => ({ ...prev, progress: audio.currentTime || 0 }));
      };
      const handleEnded = () => {
        console.log('🏁 انتهى تشغيل الملف الصوتي - استدعاء handleAudioEnded');

        // استخدام callback للحصول على أحدث state
        setState(currentState => {
          console.log('🎵 تفاصيل الصوت عند الانتهاء:', {
            currentSurah: currentState.currentSurah,
            currentAyah: currentState.currentAyah,
            targetSurah: settings.toSurah,
            targetAyah: settings.toAyah,
            autoComplete: settings.autoComplete,
            duration: audio.duration,
            currentTime: audio.currentTime,
            ended: audio.ended
          });

          // تمرير القيم الحالية مباشرة
          handleAudioEnded(currentState.currentSurah, currentState.currentAyah);
          return currentState; // لا نغير الـ state
        });
      };

      audio.addEventListener('loadstart', handleLoadStart);
      audio.addEventListener('canplay', handleCanPlay);
      audio.addEventListener('error', handleError);
      audio.addEventListener('loadedmetadata', handleLoadedMetadata);
      audio.addEventListener('timeupdate', handleTimeUpdate);
      audio.addEventListener('ended', handleEnded);

      return () => {
        audio.removeEventListener('loadstart', handleLoadStart);
        audio.removeEventListener('canplay', handleCanPlay);
        audio.removeEventListener('error', handleError);
        audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
        audio.removeEventListener('timeupdate', handleTimeUpdate);
        audio.removeEventListener('ended', handleEnded);
      };
    } catch (error) {
      console.error('Error initializing audio:', error);
      setState(prev => ({ ...prev, error: 'خطأ في تهيئة مشغل الصوت' }));
    }
  }, []);

  // دالة play المعدلة: فقط تحدث state عند forceFromStart أو custom، ولا تشغل الصوت مباشرة
  const play = useCallback((forceFromStart = false, customSurah = null, customAyah = null) => {
    let currentSurah = state.currentSurah;
    let currentAyah = state.currentAyah;
    let playSource = 'state';

    // إعادة تعيين عدادات التكرار عند بدء تشغيل جديد
    ayahRepeatCounterRef.current = 0;
    sectionRepeatCounterRef.current = 0;
    console.log('[تكرار] play: reset العدادات');

    if (!state.isPlaying || forceFromStart) {
      if (customSurah && customAyah) {
        currentSurah = customSurah;
        currentAyah = customAyah;
        playSource = 'custom params (forceFromStart)';
      } else {
        currentSurah = settings.fromSurah;
        currentAyah = settings.fromAyah;
        playSource = 'settings.fromSurah/fromAyah (forceFromStart)';
      }
      setState(prev => ({
        ...prev,
        isPlaying: true,
        currentSurah,
        currentAyah,
        currentPage: getPageFromSurahAyah(currentSurah, currentAyah)
      }));
      // لا تشغل الصوت هنا!
      return;
    } else if (customSurah && customAyah) {
      currentSurah = customSurah;
      currentAyah = customAyah;
      playSource = 'custom params (while playing)';
      setState(prev => ({
        ...prev,
        isPlaying: true,
        currentSurah,
        currentAyah,
        currentPage: getPageFromSurahAyah(currentSurah, currentAyah)
      }));
      // لا تشغل الصوت هنا!
      return;
    } else {
      // التشغيل التلقائي (الآية التالية)
      currentSurah = state.currentSurah;
      currentAyah = state.currentAyah;
      playSource = 'state (auto next)';
      setState(prev => ({ ...prev, isPlaying: true }));
      // لا تشغل الصوت هنا!
      return;
    }
  }, [state.currentSurah, state.currentAyah, state.isPlaying, settings.fromSurah, settings.fromAyah]);

  // useEffect لمراقبة تغير currentSurah/currentAyah/isPlaying وتشغيل الصوت الفعلي
  useEffect(() => {
    if (!state.isPlaying) return;
    if (!audioRef.current) return;
    // شغل الصوت عند تغير currentSurah/currentAyah
    const audioUrl = getAudioUrl(state.currentSurah, state.currentAyah, settings.selectedReciter);
    console.log('🔗 رابط الصوت النهائي:', audioUrl, '| سورة:', state.currentSurah, '| آية:', state.currentAyah, '| فولدر:', settings.selectedReciter.folder);
    audioRef.current.pause();
    audioRef.current.currentTime = 0;
    audioRef.current.src = audioUrl;
    audioRef.current.playbackRate = settings.playbackSpeed;
    setState(prev => ({ ...prev, isLoading: true, error: null }));
    audioRef.current.play().then(() => {
      setState(prev => ({ ...prev, isLoading: false }));
      // بدء التحميل المسبق للآية التالية
      preloadNextAyah(state.currentSurah, state.currentAyah);
      if (settings.autoScroll) {
        const surahNum = surahNumberFromName(state.currentSurah);
        console.log('📡 إرسال إشارة التمرير:', {
          surah: state.currentSurah,
          ayah: state.currentAyah,
          surahNumber: surahNum,
          highlight: true
        });
        window.dispatchEvent(new CustomEvent('scrollToCurrentAyah', {
          detail: {
            surah: state.currentSurah,
            ayah: state.currentAyah,
            surahNumber: surahNum,
            highlight: true // إضافة الظل الخفيف
          }
        }));
      }
    }).catch((error) => {
      setState(prev => ({ ...prev, error: 'خطأ في تشغيل الصوت: ' + error.message }));
    });
  }, [state.currentSurah, state.currentAyah, state.isPlaying, settings.selectedReciter.folder, settings.playbackSpeed, settings.autoScroll]);

  // عدل nextAyah لتقبل currentSurah/currentAyah كوسائط
  const nextAyah = useCallback((currentSurahParam?: string, currentAyahParam?: number) => {
    const currentState = currentStateRef.current;
    const currentSettings = currentSettingsRef.current;
    const currentSurah = currentSurahParam || currentState.currentSurah;
    const currentAyah = currentAyahParam || currentState.currentAyah;
    console.log('🔄 nextAyah called - Current:', currentSurah, 'آية', currentAyah);
    console.log('🎯 Target:', currentSettings.toSurah, 'آية', currentSettings.toAyah);
    console.log('⚙️ الإكمال التلقائي:', currentSettings.autoComplete);

    const currentSurahNum = surahNumberFromName(currentSurah);
    const toSurahNum = surahNumberFromName(currentSettings.toSurah);

    console.log('🔢 أرقام السور - الحالية:', currentSurahNum, 'المستهدفة:', toSurahNum);

    let nextSurah = currentSurah;
    let nextAyahNum = currentAyah + 1;

    console.log('➡️ الآية التالية المقترحة:', nextSurah, 'آية', nextAyahNum);

    // إذا انتهت السورة الحالية، انتقل للسورة التالية
    if (nextAyahNum > getSurahAyahCount(currentSurah)) {
      const nextSurahNum = currentSurahNum + 1;
      if (nextSurahNum <= 114) {
        nextSurah = surahNameFromNumber(nextSurahNum);
        nextAyahNum = 1;
        console.log('📖 انتقال لسورة جديدة:', nextSurah);
      } else {
        // وصلنا لآخر سورة، توقف
        console.log('🛑 وصلنا لآخر سورة في القرآن، توقف التشغيل');
        setState(prev => ({ ...prev, isPlaying: false }));
        return;
      }
    }

    // منطق الإكمال التلقائي المُصحح:
    const nextSurahNum = surahNumberFromName(nextSurah);

    // تحقق من شروط التوقف
    let shouldStop = false;
    let stopReason = '';

    // فحص إذا وصلنا للآية المستهدفة بالضبط
    if (nextSurahNum > toSurahNum ||
        (nextSurahNum === toSurahNum && nextAyahNum > currentSettings.toAyah)) {
      shouldStop = true;
      stopReason = `وصلنا للهدف: ${currentSettings.toSurah} آية ${currentSettings.toAyah}`;
    }

    console.log('🔍 فحص شروط التوقف:', {
      shouldStop,
      stopReason,
      nextSurahNum,
      toSurahNum,
      nextAyahNum,
      targetAyah: currentSettings.toAyah,
      autoComplete: currentSettings.autoComplete
    });

    if (shouldStop) {
      console.log('🏁', stopReason, '- توقف التشغيل');
      setState(prev => ({ ...prev, isPlaying: false }));
      return;
    }

    console.log(`➡️ الانتقال للآية التالية: ${nextSurah} - آية ${nextAyahNum}`);

    setState(prev => ({
      ...prev,
      currentSurah: nextSurah,
      currentAyah: nextAyahNum,
      currentPage: getPageFromSurahAyah(nextSurah, nextAyahNum)
    }));

    // استخدام الملف المحمل مسبقاً إذا كان متاحاً
    if (nextAudioRef.current && nextAudioRef.current.src && nextAudioRef.current.readyState >= 3) {
      console.log('🚀 تبديل فوري للملف المحمل مسبقاً');

      // إيقاف المشغل الحالي فوراً بدون fade
      if (audioRef.current) {
        audioRef.current.volume = 0; // إخفاء الصوت فوراً
        audioRef.current.pause();
        audioRef.current.currentTime = 0;
      }

      // تبديل المشغلين
      const tempRef = audioRef.current;
      audioRef.current = nextAudioRef.current;
      nextAudioRef.current = tempRef;

      // تشغيل الملف المحمل مسبقاً فوراً بدون انتظار
      audioRef.current.playbackRate = settings.playbackSpeed;
      audioRef.current.currentTime = 0;
      audioRef.current.volume = 1; // استعادة الصوت الكامل

      // تشغيل فوري
      const playPromise = audioRef.current.play();
      if (playPromise) {
        playPromise.then(() => {
          console.log('✅ تم التشغيل الفوري');
          setState(prev => ({ ...prev, isLoading: false }));
          // بدء التحميل المسبق للآية التالية
          preloadNextAyah(nextSurah, nextAyahNum);
          if (settings.autoScroll) {
            const nextSurahNum = surahNumberFromName(nextSurah);
            console.log('📡 إرسال إشارة التمرير للآية التالية:', {
              surah: nextSurah,
              ayah: nextAyahNum,
              surahNumber: nextSurahNum,
              highlight: true
            });
            window.dispatchEvent(new CustomEvent('scrollToCurrentAyah', {
              detail: {
                surah: nextSurah,
                ayah: nextAyahNum,
                surahNumber: nextSurahNum,
                highlight: true // إضافة الظل الخفيف
              }
            }));
          }
        }).catch((error) => {
          console.error('خطأ في التشغيل الفوري:', error);
          // العودة للطريقة العادية
          play(false, nextSurah, nextAyahNum);
        });
      }
    } else {
      console.log('⚠️ الملف غير جاهز، استخدام الطريقة العادية');
      // العودة للطريقة العادية إذا لم يكن هناك ملف محمل مسبقاً
      play(false, nextSurah, nextAyahNum);
    }
  }, []); // لا نحتاج dependencies لأننا نستخدم refs

  // دالة handleAudioEnded محسنة مع منطق الإكمال التلقائي الصحيح
  const isHandlingEndedRef = useRef(false);
  const currentStateRef = useRef(state);
  const currentSettingsRef = useRef(settings);

  // تحديث المراجع عند تغيير الـ state أو الإعدادات
  useEffect(() => {
    currentStateRef.current = state;
    currentSettingsRef.current = settings;
    console.log('🔄 تحديث المراجع:', {
      currentSurah: state.currentSurah,
      currentAyah: state.currentAyah,
      fromSurah: settings.fromSurah,
      fromAyah: settings.fromAyah,
      toSurah: settings.toSurah,
      toAyah: settings.toAyah
    });
  }, [state, settings]);

  const handleAudioEnded = useCallback((currentSurah?: string, currentAyah?: number) => {
    if (isHandlingEndedRef.current) return;
    isHandlingEndedRef.current = true;
    setTimeout(() => { isHandlingEndedRef.current = false; }, 500);

    const actualCurrentSurah = currentSurah || currentStateRef.current.currentSurah;
    const actualCurrentAyah = currentAyah || currentStateRef.current.currentAyah;
    const currentSettings = currentSettingsRef.current;

    const currentSurahNum = surahNumberFromName(actualCurrentSurah);
    const toSurahNum = surahNumberFromName(currentSettings.toSurah);
    const fromSurahNum = surahNumberFromName(currentSettings.fromSurah);
    const isTargetAyah = (currentSurahNum === toSurahNum && actualCurrentAyah === currentSettings.toAyah);
    const isFirstAyah = (currentSurahNum === fromSurahNum && actualCurrentAyah === currentSettings.fromAyah);

    // --- منطق تكرار الآية ---
    if (currentSettings.repeatAyah.unlimited || ayahRepeatCounterRef.current < currentSettings.repeatAyah.count - 1) {
      ayahRepeatCounterRef.current += 1;
      console.log(`[تكرار] تكرار الآية رقم ${ayahRepeatCounterRef.current} من ${currentSettings.repeatAyah.unlimited ? 'لا محدود' : currentSettings.repeatAyah.count}`);
      play(false, actualCurrentSurah, actualCurrentAyah);
      return;
    } else {
      if (ayahRepeatCounterRef.current > 0) {
        console.log(`[تكرار] انتهى تكرار الآية، إعادة العداد للصفر`);
      }
      ayahRepeatCounterRef.current = 0; // إعادة تعيين عداد الآية عند الانتقال
    }

    // --- منطق نهاية المقطع ---
    if (isTargetAyah) {
      if (currentSettings.repeatSection.unlimited || sectionRepeatCounterRef.current < currentSettings.repeatSection.count - 1) {
        sectionRepeatCounterRef.current += 1;
        ayahRepeatCounterRef.current = 0; // إعادة تعيين عداد الآية
        console.log(`[تكرار] تكرار المقطع رقم ${sectionRepeatCounterRef.current} من ${currentSettings.repeatSection.unlimited ? 'لا محدود' : currentSettings.repeatSection.count}`);
        play(false, currentSettings.fromSurah, currentSettings.fromAyah);
        return;
      } else {
        if (sectionRepeatCounterRef.current > 0) {
          console.log(`[تكرار] انتهى تكرار المقطع، إعادة العداد للصفر`);
        }
        sectionRepeatCounterRef.current = 0;
        setState(prev => ({ ...prev, isPlaying: false }));
        console.log('[تكرار] انتهى التشغيل بالكامل');
        return;
      }
    }

    // --- الانتقال للآية التالية ---
    console.log('[تكرار] الانتقال للآية التالية');
    nextAyah(actualCurrentSurah, actualCurrentAyah);
  }, []); // لا نحتاج dependencies لأننا نستخدم refs

  // إيقاف الصوت
  const pause = useCallback(() => {
    if (audioRef.current) {
      audioRef.current.pause();
    }
    // إيقاف التحميل المسبق أيضاً
    if (nextAudioRef.current) {
      nextAudioRef.current.pause();
      nextAudioRef.current.src = '';
    }
    setState(prev => ({ ...prev, isPlaying: false }));

    // إزالة تمييز الآية الحالية عند إيقاف التشغيل
    document.querySelectorAll('.current-playing-ayah').forEach(el => {
      el.classList.remove('current-playing-ayah');
    });

    // إعادة تعيين العدادات عند الإيقاف اليدوي
    ayahRepeatCounterRef.current = 0;
    sectionRepeatCounterRef.current = 0;
    console.log('[تكرار] pause: reset العدادات');
    console.log('تم إيقاف التشغيل');
  }, []);

  // تبديل التشغيل/الإيقاف
  const togglePlayPause = useCallback(() => {
    console.log('🔄 تبديل التشغيل - الحالة الحالية:', state.isPlaying);
    console.log('📋 الإعدادات الحالية:', {
      من: `${settings.fromSurah} - آية ${settings.fromAyah}`,
      إلى: `${settings.toSurah} - آية ${settings.toAyah}`,
      الإكمال_التلقائي: settings.autoComplete,
      التمرير_التلقائي: settings.autoScroll
    });

    if (state.isPlaying) {
      console.log('⏸️ إيقاف التشغيل');
      pause();
    } else {
      console.log('▶️ بدء التشغيل من البداية');
      play(true); // دائماً ابدأ من البداية عند الضغط على toggle
    }
  }, [state.isPlaying, play, pause, settings]);

  // الانتقال للآية السابقة
  const previousAyah = useCallback(() => {
    setState(prev => ({ 
      ...prev, 
      currentAyah: Math.max(1, prev.currentAyah - 1) 
    }));
  }, []);

  // تغيير مستوى الصوت
  const setVolume = useCallback((volume: number) => {
    if (audioRef.current) {
      audioRef.current.volume = Math.max(0, Math.min(1, volume));
      setState(prev => ({ ...prev, volume }));
    }
  }, []);

  // الانتقال لموضع معين في الصوت
  const seekTo = useCallback((time: number) => {
    if (audioRef.current) {
      audioRef.current.currentTime = time;
      setState(prev => ({ ...prev, progress: time }));
    }
  }, []);

  // دالة لحساب الآية التالية
  const getNextAyahInfo = (currentSurah: string, currentAyah: number) => {
    const currentSurahNum = surahNumberFromName(currentSurah);
    let nextSurah = currentSurah;
    let nextAyahNum = currentAyah + 1;

    // إذا انتهت السورة الحالية، انتقل للسورة التالية
    if (nextAyahNum > getSurahAyahCount(currentSurah)) {
      const nextSurahNum = currentSurahNum + 1;
      if (nextSurahNum <= 114) {
        nextSurah = surahNameFromNumber(nextSurahNum);
        nextAyahNum = 1;
      } else {
        return null; // وصلنا لآخر سورة
      }
    }

    return { surah: nextSurah, ayah: nextAyahNum };
  };

  // دالة لتحميل الآية التالية مسبقاً
  const preloadNextAyah = (currentSurah: string, currentAyah: number) => {
    const nextInfo = getNextAyahInfo(currentSurah, currentAyah);
    if (!nextInfo) {
      console.log('🏁 لا توجد آية تالية للتحميل المسبق');
      return;
    }

    // فحص إذا كانت الآية التالية ضمن النطاق المحدد
    const currentSettings = currentSettingsRef.current;
    const nextSurahNum = surahNumberFromName(nextInfo.surah);
    const toSurahNum = surahNumberFromName(currentSettings.toSurah);

    if (nextSurahNum > toSurahNum ||
        (nextSurahNum === toSurahNum && nextInfo.ayah > currentSettings.toAyah)) {
      console.log('🚫 الآية التالية خارج النطاق المحدد، لا حاجة للتحميل المسبق');
      return;
    }

    if (!nextAudioRef.current) {
      nextAudioRef.current = new Audio();
      nextAudioRef.current.preload = 'auto';
      nextAudioRef.current.volume = 1;
    }

    const nextUrl = getAudioUrl(nextInfo.surah, nextInfo.ayah, settings.selectedReciter);
    console.log('🔄 تحميل مسبق للآية التالية:', nextInfo.surah, 'آية', nextInfo.ayah);

    // تنظيف المشغل السابق
    nextAudioRef.current.pause();
    nextAudioRef.current.currentTime = 0;
    nextAudioRef.current.src = nextUrl;
    nextAudioRef.current.playbackRate = settings.playbackSpeed;

    // بدء التحميل
    nextAudioRef.current.load();

    // محاولة تحميل جزء من الملف مسبقاً (بدون صوت)
    nextAudioRef.current.volume = 0;
    const preloadPromise = nextAudioRef.current.play();
    if (preloadPromise) {
      preloadPromise.then(() => {
        // إيقاف التشغيل فوراً بعد بدء التحميل
        if (nextAudioRef.current) {
          nextAudioRef.current.pause();
          nextAudioRef.current.currentTime = 0;
          nextAudioRef.current.volume = 1; // استعادة الصوت للتشغيل الفعلي
        }
        console.log('✅ تم التحميل المسبق بنجاح للآية:', nextInfo.surah, 'آية', nextInfo.ayah);
      }).catch((error) => {
        // تجاهل أخطاء التحميل المسبق
        console.log('⚠️ فشل التحميل المسبق:', error.message);
        if (nextAudioRef.current) {
          nextAudioRef.current.volume = 1; // استعادة الصوت
        }
      });
    }
  };

  // قائمة القراء الذين لديهم بسملة منفصلة (محدثة من الفحص الشامل)
  const RECITERS_WITH_SEPARATE_BASMALA: string[] = [
    // حالياً لا يوجد قراء لديهم بسملة منفصلة
    // يمكن إضافة مجلدات هنا إذا تم اكتشاف قراء جدد
    // مثال: 'SomeReciter_128kbps'
  ];

  // دالة للتحقق من وجود بسملة منفصلة للقارئ
  const hasSeperateBasmala = (reciterFolder: string): boolean => {
    return RECITERS_WITH_SEPARATE_BASMALA.includes(reciterFolder);
  };

  // دالة مساعدة لبناء URL الملف الصوتي مع معالجة ذكية للبسملة
  const getAudioUrl = (surah: string, ayah: number, reciter: any): string => {
    const surahNum = surahNumberFromName(surah);
    const surahStr = surahNum.toString().padStart(3, '0');
    const ayahStr = ayah.toString().padStart(3, '0');

    console.log(`🔗 طلب صوت: سورة ${surah} (${surahNum}) آية ${ayah} - قارئ: ${reciter.name}`);

    // سورة التوبة: لا توجد بسملة في أي حال
    if (surahNum === 9) {
      console.log('🔄 سورة التوبة: لا توجد بسملة - تشغيل مباشر');
      return `https://www.everyayah.com/data/${reciter.folder}/${surahStr}${ayahStr}.mp3`;
    }

    // معالجة البسملة في بداية السور (ماعدا التوبة)
    if (ayah === 1) {
      if (hasSeperateBasmala(reciter.folder)) {
        // القراء الذين لديهم بسملة منفصلة
        console.log('🔄 قارئ لديه بسملة منفصلة - تشغيل basmala.mp3');
        return `https://www.everyayah.com/data/${reciter.folder}/basmala.mp3`;
      } else {
        // القراء الذين البسملة مدمجة في الآية الأولى (الأغلبية العظمى)
        console.log('🔄 البسملة مدمجة في الآية الأولى - تشغيل الآية الأولى مباشرة');
        return `https://www.everyayah.com/data/${reciter.folder}/${surahStr}${ayahStr}.mp3`;
      }
    }

    // الآيات العادية (غير الأولى)
    const audioUrl = `https://www.everyayah.com/data/${reciter.folder}/${surahStr}${ayahStr}.mp3`;
    console.log(`🎵 آية عادية - رابط الصوت: ${audioUrl}`);

    return audioUrl;
  };

  return {
    // الحالة
    settings,
    state,
    
    // الإجراءات الأساسية
    play,
    pause,
    togglePlayPause,
    nextAyah,
    previousAyah,
    
    // إعدادات الصوت
    setVolume,
    seekTo,
    
    // إدارة الإعدادات
    saveSettings,
    updateSection: useCallback((section: Partial<Pick<AudioSettings, 'fromSurah' | 'fromPage' | 'fromAyah' | 'toSurah' | 'toPage' | 'toAyah' | 'autoScroll'>>) => {
      console.log('🔄 تحديث المقطع:', section);
      console.log('📋 الإعدادات قبل التحديث:', {
        fromSurah: settings.fromSurah,
        fromAyah: settings.fromAyah,
        toSurah: settings.toSurah,
        toAyah: settings.toAyah
      });
      saveSettings(section);
      console.log('✅ تم حفظ الإعدادات الجديدة');

      // إذا تم تغيير السورة أو الآية، انتقل تلقائياً للموقع الجديد
      if (section.fromSurah || section.fromAyah) {
        const newSurah = section.fromSurah || settings.fromSurah;
        const newAyah = section.fromAyah || settings.fromAyah;
        // استخدم autoScroll من section إذا موجود، وإلا من settings
        const autoScroll = section.autoScroll !== undefined ? section.autoScroll : settings.autoScroll;
        console.log('📍 الانتقال التلقائي للموقع:', newSurah, 'آية', newAyah, 'autoScroll:', autoScroll);

        // تحديث الحالة الحالية للتشغيل
        setState(prev => ({
          ...prev,
          currentSurah: newSurah,
          currentAyah: newAyah,
          currentPage: getPageFromSurahAyah(newSurah, newAyah)
        }));

        if (autoScroll) {
          // أضف تأخير بسيط لضمان تحديث الواجهة أولاً
          setTimeout(() => {
            const targetPage = getPageFromSurahAyah(newSurah, newAyah);
            console.log('🧭 إرسال scrollToCurrentAyah للآية (بعد setState):', newSurah, 'آية', newAyah, 'صفحة', targetPage);
            window.dispatchEvent(new CustomEvent('scrollToCurrentAyah', {
              detail: {
                surah: newSurah,
                ayah: newAyah,
                page: targetPage,
                surahNumber: surahNumberFromName(newSurah),
                highlight: true // تمييز الآية
              }
            }));
          }, 50);
        }
      }
    }, [saveSettings, settings.fromSurah, settings.fromAyah, settings.autoScroll]),
    updateReciter: useCallback((reciter: AudioSettings['selectedReciter']) => {
      console.log('🎤 تحديث القارئ:', reciter.name);

      // إيقاف التشغيل الحالي عند تغيير القارئ
      if (state.isPlaying) {
        pause();
      }

      // تنظيف المشغلات
      if (audioRef.current) {
        audioRef.current.src = '';
      }
      if (nextAudioRef.current) {
        nextAudioRef.current.src = '';
      }

      // إزالة تمييز الآيات
      document.querySelectorAll('.current-playing-ayah').forEach(el => {
        el.classList.remove('current-playing-ayah');
      });

      saveSettings({ selectedReciter: reciter });
    }, [saveSettings, state.isPlaying, pause]),
    updateRepeatSettings: useCallback((repeat: Partial<Pick<AudioSettings, 'repeatSection' | 'repeatAyah'>>) =>
      saveSettings(repeat), [saveSettings]),
    updatePlaybackOptions: useCallback((options: Partial<Pick<AudioSettings, 'autoComplete' | 'autoScroll' | 'playbackSpeed'>>) =>
      saveSettings(options), [saveSettings]),
    
    // إعادة تعيين الإعدادات
    resetSettings: useCallback(() => {
      setSettings(DEFAULT_SETTINGS);
      localStorage.removeItem(STORAGE_KEY);
    }, [])
  };
};
