{"totalReciters": 22, "analysisByProblemType": {"جميع الملفات موجودة": [{"reciter": {"name": "محمود خليل الحصري", "folder": "Husary_64kbps", "riwaya": "ح<PERSON><PERSON>", "issue": "<PERSON>ي<PERSON> محدد"}, "results": [{"surah": 1, "exists": true, "statusCode": 200, "contentLength": 42003, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Husary_64kbps/001001.mp3"}, {"surah": 2, "exists": true, "statusCode": 200, "contentLength": 80038, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Husary_64kbps/002001.mp3"}, {"surah": 3, "exists": true, "statusCode": 200, "contentLength": 84008, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Husary_64kbps/003001.mp3"}], "missingFiles": [], "existingFiles": [{"surah": 1, "exists": true, "statusCode": 200, "contentLength": 42003, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Husary_64kbps/001001.mp3"}, {"surah": 2, "exists": true, "statusCode": 200, "contentLength": 80038, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Husary_64kbps/002001.mp3"}, {"surah": 3, "exists": true, "statusCode": 200, "contentLength": 84008, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Husary_64kbps/003001.mp3"}], "smallFiles": [], "problemType": "جميع الملفات موجودة", "solution": "قد تكون المشكلة في محتوى الملفات (لا تحتوي بسملة)"}, {"reciter": {"name": "محمود خليل الحصري", "folder": "Husary_128kbps", "riwaya": "ح<PERSON><PERSON>", "issue": "<PERSON>ي<PERSON> محدد"}, "results": [{"surah": 1, "exists": true, "statusCode": 200, "contentLength": 82164, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Husary_128kbps/001001.mp3"}, {"surah": 2, "exists": true, "statusCode": 200, "contentLength": 162631, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Husary_128kbps/002001.mp3"}, {"surah": 3, "exists": true, "statusCode": 200, "contentLength": 170572, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Husary_128kbps/003001.mp3"}], "missingFiles": [], "existingFiles": [{"surah": 1, "exists": true, "statusCode": 200, "contentLength": 82164, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Husary_128kbps/001001.mp3"}, {"surah": 2, "exists": true, "statusCode": 200, "contentLength": 162631, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Husary_128kbps/002001.mp3"}, {"surah": 3, "exists": true, "statusCode": 200, "contentLength": 170572, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Husary_128kbps/003001.mp3"}], "smallFiles": [], "problemType": "جميع الملفات موجودة", "solution": "قد تكون المشكلة في محتوى الملفات (لا تحتوي بسملة)"}, {"reciter": {"name": "محمود خليل الحصري (مجو<PERSON>)", "folder": "Husary_128kbps_Mujawwad", "riwaya": "ح<PERSON><PERSON>", "issue": "<PERSON>ي<PERSON> محدد"}, "results": [{"surah": 1, "exists": true, "statusCode": 200, "contentLength": 167946, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Husary_128kbps_Mujawwad/001001.mp3"}, {"surah": 2, "exists": true, "statusCode": 200, "contentLength": 231434, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Husary_128kbps_Mujawwad/002001.mp3"}, {"surah": 3, "exists": true, "statusCode": 200, "contentLength": 215050, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Husary_128kbps_Mujawwad/003001.mp3"}], "missingFiles": [], "existingFiles": [{"surah": 1, "exists": true, "statusCode": 200, "contentLength": 167946, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Husary_128kbps_Mujawwad/001001.mp3"}, {"surah": 2, "exists": true, "statusCode": 200, "contentLength": 231434, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Husary_128kbps_Mujawwad/002001.mp3"}, {"surah": 3, "exists": true, "statusCode": 200, "contentLength": 215050, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Husary_128kbps_Mujawwad/003001.mp3"}], "smallFiles": [], "problemType": "جميع الملفات موجودة", "solution": "قد تكون المشكلة في محتوى الملفات (لا تحتوي بسملة)"}, {"reciter": {"name": "محمود خليل الحصري (معلم)", "folder": "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_128kbps", "riwaya": "ح<PERSON><PERSON>", "issue": "<PERSON>ي<PERSON> محدد"}, "results": [{"surah": 1, "exists": true, "statusCode": 200, "contentLength": 126986, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Husary_Muallim_128kbps/001001.mp3"}, {"surah": 2, "exists": true, "statusCode": 200, "contentLength": 194570, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Husary_Muallim_128kbps/002001.mp3"}, {"surah": 3, "exists": true, "statusCode": 200, "contentLength": 200714, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Husary_Muallim_128kbps/003001.mp3"}], "missingFiles": [], "existingFiles": [{"surah": 1, "exists": true, "statusCode": 200, "contentLength": 126986, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Husary_Muallim_128kbps/001001.mp3"}, {"surah": 2, "exists": true, "statusCode": 200, "contentLength": 194570, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Husary_Muallim_128kbps/002001.mp3"}, {"surah": 3, "exists": true, "statusCode": 200, "contentLength": 200714, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Husary_Muallim_128kbps/003001.mp3"}], "smallFiles": [], "problemType": "جميع الملفات موجودة", "solution": "قد تكون المشكلة في محتوى الملفات (لا تحتوي بسملة)"}, {"reciter": {"name": "إبراهيم الأخضر", "folder": "<PERSON><PERSON><PERSON><PERSON><PERSON>_32kbps", "riwaya": "ح<PERSON><PERSON>", "issue": "<PERSON>ي<PERSON> محدد"}, "results": [{"surah": 1, "exists": true, "statusCode": 200, "contentLength": 26624, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/<PERSON>_<PERSON>_32kbps/001001.mp3"}, {"surah": 2, "exists": true, "statusCode": 200, "contentLength": 37424, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/<PERSON>_<PERSON>_32kbps/002001.mp3"}, {"surah": 3, "exists": true, "statusCode": 200, "contentLength": 36992, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/<PERSON>_<PERSON>_32kbps/003001.mp3"}], "missingFiles": [], "existingFiles": [{"surah": 1, "exists": true, "statusCode": 200, "contentLength": 26624, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/<PERSON>_<PERSON>_32kbps/001001.mp3"}, {"surah": 2, "exists": true, "statusCode": 200, "contentLength": 37424, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/<PERSON>_<PERSON>_32kbps/002001.mp3"}, {"surah": 3, "exists": true, "statusCode": 200, "contentLength": 36992, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/<PERSON>_<PERSON>_32kbps/003001.mp3"}], "smallFiles": [], "problemType": "جميع الملفات موجودة", "solution": "قد تكون المشكلة في محتوى الملفات (لا تحتوي بسملة)"}, {"reciter": {"name": "هاني الرفاعي", "folder": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>_64kbps", "riwaya": "ح<PERSON><PERSON>", "issue": "<PERSON>ي<PERSON> محدد"}, "results": [{"surah": 1, "exists": true, "statusCode": 200, "contentLength": 36152, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Hani_Rifai_64kbps/001001.mp3"}, {"surah": 2, "exists": true, "statusCode": 200, "contentLength": 44302, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Hani_Rifai_64kbps/002001.mp3"}, {"surah": 3, "exists": true, "statusCode": 200, "contentLength": 51616, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Hani_Rifai_64kbps/003001.mp3"}], "missingFiles": [], "existingFiles": [{"surah": 1, "exists": true, "statusCode": 200, "contentLength": 36152, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Hani_Rifai_64kbps/001001.mp3"}, {"surah": 2, "exists": true, "statusCode": 200, "contentLength": 44302, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Hani_Rifai_64kbps/002001.mp3"}, {"surah": 3, "exists": true, "statusCode": 200, "contentLength": 51616, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Hani_Rifai_64kbps/003001.mp3"}], "smallFiles": [], "problemType": "جميع الملفات موجودة", "solution": "قد تكون المشكلة في محتوى الملفات (لا تحتوي بسملة)"}, {"reciter": {"name": "هاني الرفاعي (جودة عالية)", "folder": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>_192kbps", "riwaya": "ح<PERSON><PERSON>", "issue": "<PERSON>ي<PERSON> محدد"}, "results": [{"surah": 1, "exists": true, "statusCode": 200, "contentLength": 107335, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Hani_Rifai_192kbps/001001.mp3"}, {"surah": 2, "exists": true, "statusCode": 200, "contentLength": 132411, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Hani_Rifai_192kbps/002001.mp3"}, {"surah": 3, "exists": true, "statusCode": 200, "contentLength": 154355, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Hani_Rifai_192kbps/003001.mp3"}], "missingFiles": [], "existingFiles": [{"surah": 1, "exists": true, "statusCode": 200, "contentLength": 107335, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Hani_Rifai_192kbps/001001.mp3"}, {"surah": 2, "exists": true, "statusCode": 200, "contentLength": 132411, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Hani_Rifai_192kbps/002001.mp3"}, {"surah": 3, "exists": true, "statusCode": 200, "contentLength": 154355, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Hani_Rifai_192kbps/003001.mp3"}], "smallFiles": [], "problemType": "جميع الملفات موجودة", "solution": "قد تكون المشكلة في محتوى الملفات (لا تحتوي بسملة)"}, {"reciter": {"name": "ياسر سلامة", "folder": "<PERSON><PERSON>_<PERSON>_128kbps", "riwaya": "ح<PERSON><PERSON>", "issue": "<PERSON>ي<PERSON> محدد"}, "results": [{"surah": 1, "exists": true, "statusCode": 200, "contentLength": 64512, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Ya<PERSON>_<PERSON>_128kbps/001001.mp3"}, {"surah": 2, "exists": true, "statusCode": 200, "contentLength": 159744, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Ya<PERSON>_<PERSON>_128kbps/002001.mp3"}, {"surah": 3, "exists": true, "statusCode": 200, "contentLength": 161230, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Ya<PERSON>_<PERSON>_128kbps/003001.mp3"}], "missingFiles": [], "existingFiles": [{"surah": 1, "exists": true, "statusCode": 200, "contentLength": 64512, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Ya<PERSON>_<PERSON>_128kbps/001001.mp3"}, {"surah": 2, "exists": true, "statusCode": 200, "contentLength": 159744, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Ya<PERSON>_<PERSON>_128kbps/002001.mp3"}, {"surah": 3, "exists": true, "statusCode": 200, "contentLength": 161230, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Ya<PERSON>_<PERSON>_128kbps/003001.mp3"}], "smallFiles": [], "problemType": "جميع الملفات موجودة", "solution": "قد تكون المشكلة في محتوى الملفات (لا تحتوي بسملة)"}, {"reciter": {"name": "عبدالله بصفر", "folder": "<PERSON><PERSON><PERSON><PERSON>far_32kbps", "riwaya": "ح<PERSON><PERSON>", "issue": "<PERSON>ي<PERSON> محدد"}, "results": [{"surah": 1, "exists": true, "statusCode": 200, "contentLength": 24320, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/<PERSON>_<PERSON>far_32kbps/001001.mp3"}, {"surah": 2, "exists": true, "statusCode": 200, "contentLength": 33968, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/<PERSON>_<PERSON>far_32kbps/002001.mp3"}, {"surah": 3, "exists": true, "statusCode": 200, "contentLength": 40160, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/<PERSON>_<PERSON>far_32kbps/003001.mp3"}], "missingFiles": [], "existingFiles": [{"surah": 1, "exists": true, "statusCode": 200, "contentLength": 24320, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/<PERSON>_<PERSON>far_32kbps/001001.mp3"}, {"surah": 2, "exists": true, "statusCode": 200, "contentLength": 33968, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/<PERSON>_<PERSON>far_32kbps/002001.mp3"}, {"surah": 3, "exists": true, "statusCode": 200, "contentLength": 40160, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/<PERSON>_<PERSON>far_32kbps/003001.mp3"}], "smallFiles": [], "problemType": "جميع الملفات موجودة", "solution": "قد تكون المشكلة في محتوى الملفات (لا تحتوي بسملة)"}, {"reciter": {"name": "عبدالله بصفر", "folder": "<PERSON><PERSON><PERSON><PERSON>_64kbps", "riwaya": "ح<PERSON><PERSON>", "issue": "<PERSON>ي<PERSON> محدد"}, "results": [{"surah": 1, "exists": true, "statusCode": 200, "contentLength": 90696, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/<PERSON>_<PERSON>_64kbps/001001.mp3"}, {"surah": 2, "exists": true, "statusCode": 200, "contentLength": 56632, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/<PERSON>_<PERSON>far_64kbps/002001.mp3"}, {"surah": 3, "exists": true, "statusCode": 200, "contentLength": 57468, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/<PERSON>_<PERSON>far_64kbps/003001.mp3"}], "missingFiles": [], "existingFiles": [{"surah": 1, "exists": true, "statusCode": 200, "contentLength": 90696, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/<PERSON>_<PERSON>_64kbps/001001.mp3"}, {"surah": 2, "exists": true, "statusCode": 200, "contentLength": 56632, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/<PERSON>_<PERSON>far_64kbps/002001.mp3"}, {"surah": 3, "exists": true, "statusCode": 200, "contentLength": 57468, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/<PERSON>_<PERSON>far_64kbps/003001.mp3"}], "smallFiles": [], "problemType": "جميع الملفات موجودة", "solution": "قد تكون المشكلة في محتوى الملفات (لا تحتوي بسملة)"}, {"reciter": {"name": "عبدالله بصفر (جودة عالية)", "folder": "<PERSON><PERSON><PERSON><PERSON>_192kbps", "riwaya": "ح<PERSON><PERSON>", "issue": "<PERSON>ي<PERSON> محدد"}, "results": [{"surah": 1, "exists": true, "statusCode": 200, "contentLength": 133666, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/<PERSON>_<PERSON>_192kbps/001001.mp3"}, {"surah": 2, "exists": true, "statusCode": 200, "contentLength": 169402, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/<PERSON>_<PERSON>_192kbps/002001.mp3"}, {"surah": 3, "exists": true, "statusCode": 200, "contentLength": 171909, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/<PERSON>_<PERSON>_192kbps/003001.mp3"}], "missingFiles": [], "existingFiles": [{"surah": 1, "exists": true, "statusCode": 200, "contentLength": 133666, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/<PERSON>_<PERSON>_192kbps/001001.mp3"}, {"surah": 2, "exists": true, "statusCode": 200, "contentLength": 169402, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/<PERSON>_<PERSON>_192kbps/002001.mp3"}, {"surah": 3, "exists": true, "statusCode": 200, "contentLength": 171909, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/<PERSON>_<PERSON>_192kbps/003001.mp3"}], "smallFiles": [], "problemType": "جميع الملفات موجودة", "solution": "قد تكون المشكلة في محتوى الملفات (لا تحتوي بسملة)"}, {"reciter": {"name": "علي عبدالرحمن الحذيفي", "folder": "Hudhaify_32kbps", "riwaya": "ح<PERSON><PERSON>", "issue": "<PERSON>ي<PERSON> محدد"}, "results": [{"surah": 1, "exists": true, "statusCode": 200, "contentLength": 48799, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Hudhaify_32kbps/001001.mp3"}, {"surah": 2, "exists": true, "statusCode": 200, "contentLength": 31443, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Hudhaify_32kbps/002001.mp3"}, {"surah": 3, "exists": true, "statusCode": 200, "contentLength": 31443, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Hudhaify_32kbps/003001.mp3"}], "missingFiles": [], "existingFiles": [{"surah": 1, "exists": true, "statusCode": 200, "contentLength": 48799, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Hudhaify_32kbps/001001.mp3"}, {"surah": 2, "exists": true, "statusCode": 200, "contentLength": 31443, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Hudhaify_32kbps/002001.mp3"}, {"surah": 3, "exists": true, "statusCode": 200, "contentLength": 31443, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Hudhaify_32kbps/003001.mp3"}], "smallFiles": [], "problemType": "جميع الملفات موجودة", "solution": "قد تكون المشكلة في محتوى الملفات (لا تحتوي بسملة)"}, {"reciter": {"name": "علي عبدالرحمن الحذيفي", "folder": "Hudhaify_64kbps", "riwaya": "ح<PERSON><PERSON>", "issue": "<PERSON>ي<PERSON> محدد"}, "results": [{"surah": 1, "exists": true, "statusCode": 200, "contentLength": 57259, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Hudhaify_64kbps/001001.mp3"}, {"surah": 2, "exists": true, "statusCode": 200, "contentLength": 77948, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Hudhaify_64kbps/002001.mp3"}, {"surah": 3, "exists": true, "statusCode": 200, "contentLength": 77112, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Hudhaify_64kbps/003001.mp3"}], "missingFiles": [], "existingFiles": [{"surah": 1, "exists": true, "statusCode": 200, "contentLength": 57259, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Hudhaify_64kbps/001001.mp3"}, {"surah": 2, "exists": true, "statusCode": 200, "contentLength": 77948, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Hudhaify_64kbps/002001.mp3"}, {"surah": 3, "exists": true, "statusCode": 200, "contentLength": 77112, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Hudhaify_64kbps/003001.mp3"}], "smallFiles": [], "problemType": "جميع الملفات موجودة", "solution": "قد تكون المشكلة في محتوى الملفات (لا تحتوي بسملة)"}, {"reciter": {"name": "خليفة الطنيجي", "folder": "<PERSON><PERSON><PERSON>_al_tunaiji_64kbps", "riwaya": "ح<PERSON><PERSON>", "issue": "<PERSON>ي<PERSON> محدد"}, "results": [{"surah": 1, "exists": true, "statusCode": 200, "contentLength": 79540, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/khale<PERSON>_al_tunaiji_64kbps/001001.mp3"}, {"surah": 2, "exists": true, "statusCode": 200, "contentLength": 62830, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/khale<PERSON>_al_tunaiji_64kbps/002001.mp3"}, {"surah": 3, "exists": true, "statusCode": 200, "contentLength": 72652, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/khale<PERSON>_al_tunaiji_64kbps/003001.mp3"}], "missingFiles": [], "existingFiles": [{"surah": 1, "exists": true, "statusCode": 200, "contentLength": 79540, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/khale<PERSON>_al_tunaiji_64kbps/001001.mp3"}, {"surah": 2, "exists": true, "statusCode": 200, "contentLength": 62830, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/khale<PERSON>_al_tunaiji_64kbps/002001.mp3"}, {"surah": 3, "exists": true, "statusCode": 200, "contentLength": 72652, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/khale<PERSON>_al_tunaiji_64kbps/003001.mp3"}], "smallFiles": [], "problemType": "جميع الملفات موجودة", "solution": "قد تكون المشكلة في محتوى الملفات (لا تحتوي بسملة)"}, {"reciter": {"name": "<PERSON>ي<PERSON><PERSON> سويد", "folder": "<PERSON><PERSON>_<PERSON>d_64kbps", "riwaya": "ح<PERSON><PERSON>", "issue": "<PERSON>ي<PERSON> محدد"}, "results": [{"surah": 1, "exists": true, "statusCode": 200, "contentLength": 55104, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/<PERSON><PERSON>_Sowaid_64kbps/001001.mp3"}, {"surah": 2, "exists": true, "statusCode": 200, "contentLength": 58944, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/<PERSON><PERSON>_Sowaid_64kbps/002001.mp3"}, {"surah": 3, "exists": true, "statusCode": 200, "contentLength": 62400, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/<PERSON><PERSON>_Sowaid_64kbps/003001.mp3"}], "missingFiles": [], "existingFiles": [{"surah": 1, "exists": true, "statusCode": 200, "contentLength": 55104, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/<PERSON><PERSON>_Sowaid_64kbps/001001.mp3"}, {"surah": 2, "exists": true, "statusCode": 200, "contentLength": 58944, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/<PERSON><PERSON>_Sowaid_64kbps/002001.mp3"}, {"surah": 3, "exists": true, "statusCode": 200, "contentLength": 62400, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/<PERSON><PERSON>_Sowaid_64kbps/003001.mp3"}], "smallFiles": [], "problemType": "جميع الملفات موجودة", "solution": "قد تكون المشكلة في محتوى الملفات (لا تحتوي بسملة)"}, {"reciter": {"name": "<PERSON><PERSON><PERSON><PERSON>و<PERSON>", "folder": "<PERSON>_<PERSON><PERSON>youb_32kbps", "riwaya": "ح<PERSON><PERSON>", "issue": "<PERSON>ي<PERSON> محدد"}, "results": [{"surah": 1, "exists": true, "statusCode": 200, "contentLength": 26624, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/<PERSON>_<PERSON><PERSON>youb_32kbps/001001.mp3"}, {"surah": 2, "exists": true, "statusCode": 200, "contentLength": 40960, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/<PERSON>_<PERSON>yyoub_32kbps/002001.mp3"}, {"surah": 3, "exists": true, "statusCode": 200, "contentLength": 40960, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/<PERSON>_<PERSON>yyoub_32kbps/003001.mp3"}], "missingFiles": [], "existingFiles": [{"surah": 1, "exists": true, "statusCode": 200, "contentLength": 26624, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/<PERSON>_<PERSON><PERSON>youb_32kbps/001001.mp3"}, {"surah": 2, "exists": true, "statusCode": 200, "contentLength": 40960, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/<PERSON>_<PERSON>yyoub_32kbps/002001.mp3"}, {"surah": 3, "exists": true, "statusCode": 200, "contentLength": 40960, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/<PERSON>_<PERSON>yyoub_32kbps/003001.mp3"}], "smallFiles": [], "problemType": "جميع الملفات موجودة", "solution": "قد تكون المشكلة في محتوى الملفات (لا تحتوي بسملة)"}, {"reciter": {"name": "إبراهيم الدوسري", "folder": "warsh/warsh_i<PERSON><PERSON>_aldosary_128kbps", "riwaya": "ورش", "issue": "<PERSON>ي<PERSON> محدد"}, "results": [{"surah": 1, "exists": true, "statusCode": 200, "contentLength": 105324, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/warsh/warsh_ibra<PERSON>_aldosary_128kbps/001001.mp3"}, {"surah": 2, "exists": true, "statusCode": 200, "contentLength": 125517, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/warsh/warsh_ibra<PERSON>_aldosary_128kbps/002001.mp3"}, {"surah": 3, "exists": true, "statusCode": 200, "contentLength": 169690, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/warsh/warsh_ibra<PERSON>_aldosary_128kbps/003001.mp3"}], "missingFiles": [], "existingFiles": [{"surah": 1, "exists": true, "statusCode": 200, "contentLength": 105324, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/warsh/warsh_ibra<PERSON>_aldosary_128kbps/001001.mp3"}, {"surah": 2, "exists": true, "statusCode": 200, "contentLength": 125517, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/warsh/warsh_ibra<PERSON>_aldosary_128kbps/002001.mp3"}, {"surah": 3, "exists": true, "statusCode": 200, "contentLength": 169690, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/warsh/warsh_ibra<PERSON>_aldosary_128kbps/003001.mp3"}], "smallFiles": [], "problemType": "جميع الملفات موجودة", "solution": "قد تكون المشكلة في محتوى الملفات (لا تحتوي بسملة)"}, {"reciter": {"name": "عب<PERSON> البا<PERSON>ط عب<PERSON> الصمد (ورش)", "folder": "warsh/warsh_<PERSON>_<PERSON>_128kbps", "riwaya": "ورش", "issue": "<PERSON>ي<PERSON> محدد"}, "results": [{"surah": 1, "exists": true, "statusCode": 200, "contentLength": 88318, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/warsh/warsh_<PERSON>_<PERSON>_128kbps/001001.mp3"}, {"surah": 2, "exists": true, "statusCode": 200, "contentLength": 312344, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/warsh/warsh_<PERSON>_<PERSON>_128kbps/002001.mp3"}, {"surah": 3, "exists": true, "statusCode": 200, "contentLength": 317359, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/warsh/warsh_<PERSON>_<PERSON>_128kbps/003001.mp3"}], "missingFiles": [], "existingFiles": [{"surah": 1, "exists": true, "statusCode": 200, "contentLength": 88318, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/warsh/warsh_<PERSON>_<PERSON>_128kbps/001001.mp3"}, {"surah": 2, "exists": true, "statusCode": 200, "contentLength": 312344, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/warsh/warsh_<PERSON>_<PERSON>_128kbps/002001.mp3"}, {"surah": 3, "exists": true, "statusCode": 200, "contentLength": 317359, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/warsh/warsh_<PERSON>_<PERSON>_128kbps/003001.mp3"}], "smallFiles": [], "problemType": "جميع الملفات موجودة", "solution": "قد تكون المشكلة في محتوى الملفات (لا تحتوي بسملة)"}], "بعض الملفات مفقودة": [{"reciter": {"name": "محمود خليل الحصري (مجو<PERSON>)", "folder": "Husary_Mujawwad_64kbps", "riwaya": "ح<PERSON><PERSON>", "issue": "<PERSON>ي<PERSON> محدد"}, "results": [{"surah": 1, "exists": false, "statusCode": 0, "contentLength": 0, "contentType": "", "error": "", "url": "https://everyayah.com/data/Husary_Mujawwad_64kbps/001001.mp3"}, {"surah": 2, "exists": true, "statusCode": 200, "contentLength": 115773, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Husary_Mujawwad_64kbps/002001.mp3"}, {"surah": 3, "exists": true, "statusCode": 200, "contentLength": 108041, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Husary_Mujawwad_64kbps/003001.mp3"}], "missingFiles": [{"surah": 1, "exists": false, "statusCode": 0, "contentLength": 0, "contentType": "", "error": "", "url": "https://everyayah.com/data/Husary_Mujawwad_64kbps/001001.mp3"}], "existingFiles": [{"surah": 2, "exists": true, "statusCode": 200, "contentLength": 115773, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Husary_Mujawwad_64kbps/002001.mp3"}, {"surah": 3, "exists": true, "statusCode": 200, "contentLength": 108041, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Husary_Mujawwad_64kbps/003001.mp3"}], "smallFiles": [], "problemType": "بعض الملفات مفقودة", "solution": "قد تكون مشكلة في تسمية الملفات أو المجلد"}, {"reciter": {"name": "محمد صديق المنشاوي (جودة منخفضة جداً)", "folder": "Menshawi_16kbps", "riwaya": "ح<PERSON><PERSON>", "issue": "<PERSON>ي<PERSON> محدد"}, "results": [{"surah": 1, "exists": true, "statusCode": 200, "contentLength": 8777, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Menshawi_16kbps/001001.mp3"}, {"surah": 2, "exists": true, "statusCode": 200, "contentLength": 14315, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Menshawi_16kbps/002001.mp3"}, {"surah": 3, "exists": false, "statusCode": 0, "contentLength": 0, "contentType": "", "error": "", "url": "https://everyayah.com/data/Menshawi_16kbps/003001.mp3"}], "missingFiles": [{"surah": 3, "exists": false, "statusCode": 0, "contentLength": 0, "contentType": "", "error": "", "url": "https://everyayah.com/data/Menshawi_16kbps/003001.mp3"}], "existingFiles": [{"surah": 1, "exists": true, "statusCode": 200, "contentLength": 8777, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Menshawi_16kbps/001001.mp3"}, {"surah": 2, "exists": true, "statusCode": 200, "contentLength": 14315, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/Menshawi_16kbps/002001.mp3"}], "smallFiles": [], "problemType": "بعض الملفات مفقودة", "solution": "قد تكون مشكلة في تسمية الملفات أو المجلد"}, {"reciter": {"name": "مصط<PERSON>ى إسماعيل", "folder": "<PERSON><PERSON><PERSON>_48kbps", "riwaya": "ح<PERSON><PERSON>", "issue": "غير شغال - يحتاج مراجعة"}, "results": [{"surah": 1, "exists": true, "statusCode": 200, "contentLength": 57224, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/<PERSON>_<PERSON>_48kbps/001001.mp3"}, {"surah": 2, "exists": true, "statusCode": 200, "contentLength": 84653, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/<PERSON>_<PERSON>_48kbps/002001.mp3"}, {"surah": 3, "exists": false, "statusCode": 404, "contentLength": 0, "contentType": "text/html", "url": "https://everyayah.com/data/<PERSON>_<PERSON>_48kbps/003001.mp3"}], "missingFiles": [{"surah": 3, "exists": false, "statusCode": 404, "contentLength": 0, "contentType": "text/html", "url": "https://everyayah.com/data/<PERSON>_<PERSON>_48kbps/003001.mp3"}], "existingFiles": [{"surah": 1, "exists": true, "statusCode": 200, "contentLength": 57224, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/<PERSON>_<PERSON>_48kbps/001001.mp3"}, {"surah": 2, "exists": true, "statusCode": 200, "contentLength": 84653, "contentType": "audio/mpeg", "url": "https://everyayah.com/data/<PERSON>_<PERSON>_48kbps/002001.mp3"}], "smallFiles": [], "problemType": "بعض الملفات مفقودة", "solution": "قد تكون مشكلة في تسمية الملفات أو المجلد"}], "جميع الملفات مفقودة": [{"reciter": {"name": "محمد صديق المنشاوي (معلم)", "folder": "Minshawy_Muallim_128kbps", "riwaya": "ح<PERSON><PERSON>", "issue": "<PERSON>ي<PERSON> محدد"}, "results": [{"surah": 1, "exists": false, "statusCode": 404, "contentLength": 0, "contentType": "text/html", "url": "https://everyayah.com/data/Minshawy_Muallim_128kbps/001001.mp3"}, {"surah": 2, "exists": false, "statusCode": 404, "contentLength": 0, "contentType": "text/html", "url": "https://everyayah.com/data/Minshawy_Muallim_128kbps/002001.mp3"}, {"surah": 3, "exists": false, "statusCode": 404, "contentLength": 0, "contentType": "text/html", "url": "https://everyayah.com/data/Minshawy_Muallim_128kbps/003001.mp3"}], "missingFiles": [{"surah": 1, "exists": false, "statusCode": 404, "contentLength": 0, "contentType": "text/html", "url": "https://everyayah.com/data/Minshawy_Muallim_128kbps/001001.mp3"}, {"surah": 2, "exists": false, "statusCode": 404, "contentLength": 0, "contentType": "text/html", "url": "https://everyayah.com/data/Minshawy_Muallim_128kbps/002001.mp3"}, {"surah": 3, "exists": false, "statusCode": 404, "contentLength": 0, "contentType": "text/html", "url": "https://everyayah.com/data/Minshawy_Muallim_128kbps/003001.mp3"}], "existingFiles": [], "smallFiles": [], "problemType": "جميع الملفات مفقودة", "solution": "المجلد غير موجود أو القارئ غير متاح على السيرفر"}]}, "detailedResults": [{"name": "محمود خليل الحصري", "folder": "Husary_64kbps", "riwaya": "ح<PERSON><PERSON>", "problemType": "جميع الملفات موجودة", "solution": "قد تكون المشكلة في محتوى الملفات (لا تحتوي بسملة)", "missingFiles": 0, "smallFiles": 0, "missingSurahs": [], "smallFileDetails": []}, {"name": "محمود خليل الحصري", "folder": "Husary_128kbps", "riwaya": "ح<PERSON><PERSON>", "problemType": "جميع الملفات موجودة", "solution": "قد تكون المشكلة في محتوى الملفات (لا تحتوي بسملة)", "missingFiles": 0, "smallFiles": 0, "missingSurahs": [], "smallFileDetails": []}, {"name": "محمود خليل الحصري (مجو<PERSON>)", "folder": "Husary_Mujawwad_64kbps", "riwaya": "ح<PERSON><PERSON>", "problemType": "بعض الملفات مفقودة", "solution": "قد تكون مشكلة في تسمية الملفات أو المجلد", "missingFiles": 1, "smallFiles": 0, "missingSurahs": [1], "smallFileDetails": []}, {"name": "محمود خليل الحصري (مجو<PERSON>)", "folder": "Husary_128kbps_Mujawwad", "riwaya": "ح<PERSON><PERSON>", "problemType": "جميع الملفات موجودة", "solution": "قد تكون المشكلة في محتوى الملفات (لا تحتوي بسملة)", "missingFiles": 0, "smallFiles": 0, "missingSurahs": [], "smallFileDetails": []}, {"name": "محمود خليل الحصري (معلم)", "folder": "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_128kbps", "riwaya": "ح<PERSON><PERSON>", "problemType": "جميع الملفات موجودة", "solution": "قد تكون المشكلة في محتوى الملفات (لا تحتوي بسملة)", "missingFiles": 0, "smallFiles": 0, "missingSurahs": [], "smallFileDetails": []}, {"name": "محمد صديق المنشاوي (جودة منخفضة جداً)", "folder": "Menshawi_16kbps", "riwaya": "ح<PERSON><PERSON>", "problemType": "بعض الملفات مفقودة", "solution": "قد تكون مشكلة في تسمية الملفات أو المجلد", "missingFiles": 1, "smallFiles": 0, "missingSurahs": [3], "smallFileDetails": []}, {"name": "محمد صديق المنشاوي (معلم)", "folder": "Minshawy_Muallim_128kbps", "riwaya": "ح<PERSON><PERSON>", "problemType": "جميع الملفات مفقودة", "solution": "المجلد غير موجود أو القارئ غير متاح على السيرفر", "missingFiles": 3, "smallFiles": 0, "missingSurahs": [1, 2, 3], "smallFileDetails": []}, {"name": "مصط<PERSON>ى إسماعيل", "folder": "<PERSON><PERSON><PERSON>_48kbps", "riwaya": "ح<PERSON><PERSON>", "problemType": "بعض الملفات مفقودة", "solution": "قد تكون مشكلة في تسمية الملفات أو المجلد", "missingFiles": 1, "smallFiles": 0, "missingSurahs": [3], "smallFileDetails": []}, {"name": "إبراهيم الأخضر", "folder": "<PERSON><PERSON><PERSON><PERSON><PERSON>_32kbps", "riwaya": "ح<PERSON><PERSON>", "problemType": "جميع الملفات موجودة", "solution": "قد تكون المشكلة في محتوى الملفات (لا تحتوي بسملة)", "missingFiles": 0, "smallFiles": 0, "missingSurahs": [], "smallFileDetails": []}, {"name": "هاني الرفاعي", "folder": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>_64kbps", "riwaya": "ح<PERSON><PERSON>", "problemType": "جميع الملفات موجودة", "solution": "قد تكون المشكلة في محتوى الملفات (لا تحتوي بسملة)", "missingFiles": 0, "smallFiles": 0, "missingSurahs": [], "smallFileDetails": []}, {"name": "هاني الرفاعي (جودة عالية)", "folder": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>_192kbps", "riwaya": "ح<PERSON><PERSON>", "problemType": "جميع الملفات موجودة", "solution": "قد تكون المشكلة في محتوى الملفات (لا تحتوي بسملة)", "missingFiles": 0, "smallFiles": 0, "missingSurahs": [], "smallFileDetails": []}, {"name": "ياسر سلامة", "folder": "<PERSON><PERSON>_<PERSON>_128kbps", "riwaya": "ح<PERSON><PERSON>", "problemType": "جميع الملفات موجودة", "solution": "قد تكون المشكلة في محتوى الملفات (لا تحتوي بسملة)", "missingFiles": 0, "smallFiles": 0, "missingSurahs": [], "smallFileDetails": []}, {"name": "عبدالله بصفر", "folder": "<PERSON><PERSON><PERSON><PERSON>far_32kbps", "riwaya": "ح<PERSON><PERSON>", "problemType": "جميع الملفات موجودة", "solution": "قد تكون المشكلة في محتوى الملفات (لا تحتوي بسملة)", "missingFiles": 0, "smallFiles": 0, "missingSurahs": [], "smallFileDetails": []}, {"name": "عبدالله بصفر", "folder": "<PERSON><PERSON><PERSON><PERSON>_64kbps", "riwaya": "ح<PERSON><PERSON>", "problemType": "جميع الملفات موجودة", "solution": "قد تكون المشكلة في محتوى الملفات (لا تحتوي بسملة)", "missingFiles": 0, "smallFiles": 0, "missingSurahs": [], "smallFileDetails": []}, {"name": "عبدالله بصفر (جودة عالية)", "folder": "<PERSON><PERSON><PERSON><PERSON>_192kbps", "riwaya": "ح<PERSON><PERSON>", "problemType": "جميع الملفات موجودة", "solution": "قد تكون المشكلة في محتوى الملفات (لا تحتوي بسملة)", "missingFiles": 0, "smallFiles": 0, "missingSurahs": [], "smallFileDetails": []}, {"name": "علي عبدالرحمن الحذيفي", "folder": "Hudhaify_32kbps", "riwaya": "ح<PERSON><PERSON>", "problemType": "جميع الملفات موجودة", "solution": "قد تكون المشكلة في محتوى الملفات (لا تحتوي بسملة)", "missingFiles": 0, "smallFiles": 0, "missingSurahs": [], "smallFileDetails": []}, {"name": "علي عبدالرحمن الحذيفي", "folder": "Hudhaify_64kbps", "riwaya": "ح<PERSON><PERSON>", "problemType": "جميع الملفات موجودة", "solution": "قد تكون المشكلة في محتوى الملفات (لا تحتوي بسملة)", "missingFiles": 0, "smallFiles": 0, "missingSurahs": [], "smallFileDetails": []}, {"name": "خليفة الطنيجي", "folder": "<PERSON><PERSON><PERSON>_al_tunaiji_64kbps", "riwaya": "ح<PERSON><PERSON>", "problemType": "جميع الملفات موجودة", "solution": "قد تكون المشكلة في محتوى الملفات (لا تحتوي بسملة)", "missingFiles": 0, "smallFiles": 0, "missingSurahs": [], "smallFileDetails": []}, {"name": "<PERSON>ي<PERSON><PERSON> سويد", "folder": "<PERSON><PERSON>_<PERSON>d_64kbps", "riwaya": "ح<PERSON><PERSON>", "problemType": "جميع الملفات موجودة", "solution": "قد تكون المشكلة في محتوى الملفات (لا تحتوي بسملة)", "missingFiles": 0, "smallFiles": 0, "missingSurahs": [], "smallFileDetails": []}, {"name": "<PERSON><PERSON><PERSON><PERSON>و<PERSON>", "folder": "<PERSON>_<PERSON><PERSON>youb_32kbps", "riwaya": "ح<PERSON><PERSON>", "problemType": "جميع الملفات موجودة", "solution": "قد تكون المشكلة في محتوى الملفات (لا تحتوي بسملة)", "missingFiles": 0, "smallFiles": 0, "missingSurahs": [], "smallFileDetails": []}, {"name": "إبراهيم الدوسري", "folder": "warsh/warsh_i<PERSON><PERSON>_aldosary_128kbps", "riwaya": "ورش", "problemType": "جميع الملفات موجودة", "solution": "قد تكون المشكلة في محتوى الملفات (لا تحتوي بسملة)", "missingFiles": 0, "smallFiles": 0, "missingSurahs": [], "smallFileDetails": []}, {"name": "عب<PERSON> البا<PERSON>ط عب<PERSON> الصمد (ورش)", "folder": "warsh/warsh_<PERSON>_<PERSON>_128kbps", "riwaya": "ورش", "problemType": "جميع الملفات موجودة", "solution": "قد تكون المشكلة في محتوى الملفات (لا تحتوي بسملة)", "missingFiles": 0, "smallFiles": 0, "missingSurahs": [], "smallFileDetails": []}], "timestamp": "2025-07-26T21:52:00.815Z"}