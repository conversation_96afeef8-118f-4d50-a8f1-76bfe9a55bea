import { useBookmarks } from "../hooks/useBookmarks";

interface LastReadProps {
  onContinue: (page: number, aya: number) => void;
  onLastPage: (page: number) => void;
}

const LastRead = ({ onContinue, onLastPage }: LastReadProps) => {
  const { bookmarks, removeBookmark } = useBookmarks();

  // ترتيب الفواصل: الأحدث أولاً (لو فيه timestamp مستقبلاً)
  const sortedBookmarks = [...bookmarks].reverse();

  // دالة لعرض التاريخ (يمكنك تحسينها لاحقاً)
  const getDisplayDate = (timestamp?: string) => {
    if (!timestamp) return "";
    const date = new Date(timestamp);
    return date.toLocaleString("ar-EG");
  };

  return (
    <div className="bg-gray-900 p-4 space-y-4">
      <h3 className="text-white font-bold mb-3 text-right">الفواصل المحفوظة</h3>
      <div className="flex gap-3 overflow-x-auto pb-2">
        {sortedBookmarks.length > 0 ? (
          sortedBookmarks.map((bookmark, index) => (
            <div
              key={index}
              className="min-w-[200px] bg-gray-800 rounded-lg p-3 hover:bg-gray-700 transition-colors border-r-4 relative group"
              style={{ borderRightColor: bookmark.type === 'ayah' ? bookmark.color : '#FFD700' }}
            >
              {/* زر الحذف */}
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  removeBookmark(bookmark);
                }}
                className="absolute -top-2 -left-2 w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center text-xs opacity-0 group-hover:opacity-100 transition-opacity z-10"
                title="حذف الفاصل"
              >
                ×
              </button>

              <div
                onClick={() => {
                  if (bookmark.type === 'ayah') {
                    onContinue && onContinue(bookmark.surah, bookmark.ayah);
                  } else if (bookmark.type === 'page') {
                    onLastPage && onLastPage(bookmark.page);
                  }
                }}
                className="text-right cursor-pointer"
              >
                <div className="flex items-center justify-between mb-1">
                  {bookmark.type === 'ayah' ? (
                    <div className="w-3 h-3 rounded-full" style={{ backgroundColor: bookmark.color }}></div>
                  ) : (
                    <div className="w-3 h-3 rounded-full" style={{ backgroundColor: '#FFD700' }}></div>
                  )}
                  <h4 className="text-white font-medium text-sm">
                    {bookmark.type === 'ayah'
                      ? `سورة ${bookmark.surah} • آية ${bookmark.ayah}`
                      : `صفحة ${bookmark.page}`}
                  </h4>
                </div>
                {/* يمكنك إضافة مزيد من التفاصيل هنا */}
              </div>
            </div>
          ))
        ) : (
          <div className="flex-1 bg-gray-800 rounded-lg p-4 text-center text-gray-400">
            لا توجد فواصل محفوظة بعد
          </div>
        )}
      </div>
    </div>
  );
};

export default LastRead;
