import React, { useRef } from "react";

const DUA_LIST = [
  "اللَّهُمَّ اجعلني ممن يحل حلاله، ويحرم حرامه، ويعمل بمحكمه، ويؤمن بمتشابهه، ويتلوه حق تلاوته.",
  "اللَّهُمَّ اجعلني ممن يقيم حروفه وحدوده، ولا تجعلني ممن يقيم حروفه ويضيع حدوده.",
  "اللَّهُمَّ ألبسني به الحلل، وأسكني به الظلل، وادفع عني به النقم، وزدني به من النعم، يا ذا الجلال والإكرام.",
  "اللَّهُمَّ اجعلني من أهل القرآن الذين هم أهلك وخاصتك، يا ذا الجلال والإكرام.",
  "اللَّهُمَّ اجعل القرآن العظيم لقلبي ضياءً، ولبصري جلاءً، ولسقمي دواءً، ولذنوبي ممحصًا، وعن النيران مخلصًا.",
  "اللَّهُمَّ اجعله شفيعًا لي، وحجة لي لا حجة علي.",
  "اللَّهُمَّ اجعلني ممن قاده القرآن إلى الجنان، ولا تجعلني ممن أعرض عنه القرآن فزجَّ في قفاه في النار، يا واحد يا قهار.",
  "اللَّهُمَّ وفقني في هذه الليلة المباركة لما تحب وترضى.",
  "اللَّهُمَّ انقلني بالقرآن من الشقاء إلى السعادة، ومن النار إلى الجنة، ومن الضلالة إلى الهداية، ومن الذل إلى العز، يا ذا الجلال والإكرام، ومن أنواع الشرور كلها إلى أنواع الخير كلها يا حي يا قيوم.",
  "اللَّهُمَّ وفقني في ليلتي هذه إلى ما تحب وترضى، وفي كل أعمالي، يا حي يا قيوم.",
  "اللَّهُمَّ صل وسلم وبارك على نبينا محمد، ولا تجعل لي في مقامي هذا ذنبًا إلا غفرته، ولا همًا إلا فرجته، ولا كربًا إلا نفسته، ولا دينًا إلا قضيته، ولا مريضًا إلا شفيته، ولا ميتًا إلا رحمته، ولا مظلومًا إلا نصرته، ولا ظالمًا إلا قصمته، ولا عسيرًا إلا يسرته، ولا حاجة من حوائج الدنيا والآخرة هي لك رضا ولي فيها صلاح إلا أعنتني على قضائها ويسرتها، برحمتك يا أرحم الراحمين.",
  "اللَّهُمَّ اجعلني لكتابك من التالين، وعند ختمه من الفائزين.",
  "اللَّهُمَّ اجعلني عند ختمه من الفائزين، وعند النعماء من الشاكرين، وعند البلاء من الصابرين، ولا تجعلني ممن استهوته الشياطين فشغلته بالدنيا عن الدين، فأصبح من النادمين، وفي الآخرة من الخاسرين.",
  "اللَّهُمَّ قد ختمت كتابك، فلا تطردني عن بابك، فإن طردتني فإنه لا حول لي ولا قوة إلا بك.",
  "اللَّهُمَّ إني عبدك، وابن عبدك، وابن أمتك، ناصيتي بيدك، ماضٍ في حكمك، عدل في قضاؤك، أسألك بكل اسم هو لك سميت به نفسك، أو علمته أحدًا من خلقك، أو أنزلته في كتابك، أو استأثرت به في علم الغيب عندك، أن تجعل القرآن ربيع قلبي ونور صدري، وجلاء حزني، وذهاب همي.",
  "اللَّهُمَّ ارفعني وارفعني بالقرآن العظيم الذي أيدت سلطانه، وقلت يا أعز من قائل سبحانه: فإذا قرأناه فاتبع قرآنه ثم إن علينا بيانه [القيامة:18-19]",
  "أحسن كتابك نظامًا، وأفصحها كلامًا، وأبينها حلالًا وحرامًا، ظاهر البرهان، محكم البيان، محروس من الزيادة والنقصان، فيه وعد ووعيد، وتخويف وتهديد، لا يأتيه الباطل من بين يديه ولا من خلفه تنزيل من حكيم حميد [فصلت:42].",
  "اللَّهُمَّ ذكرني منه ما نسيت، وعلمني منه ما جهلت، وارزقني تلاوته آناء الليل وأطراف النهار على الوجه الذي يرضيك عني.",
  "اللَّهُمَّ ارفع عنا في كل مكان المحن والبلايا، والفتن.",
  "اللَّهُمَّ ادفع عنا الغلاء، والوباء، والربا، والزنا، والزلازل، والمحن، وسوء الفتن، يا ذا الجلال والإكرام.",
  "اللَّهُمَّ وفقني في هذه الليلة المباركة وفي سائر الليالي لالتزام طريق التوبة النصوح.",
  "اللَّهُمَّ ارفع الضر عن المتضررين، والبأساء عن البائسين.",
  "اللَّهُمَّ إنك عفو تحب العفو فاعف عني، اللَّهُمَّ اجعل خير عمري أواخره، وخير أعمالي خواتمه، وخير أيامي يوم ألقاك.",
  "اللَّهُمَّ إني أسألك مسألة الخائفين، وانتهل إليك ابتهال المذنبين، ابتهال ودعاء من خضعت لك رقابهم، ورغمت لك أنوفهم، اللَّهُمَّ فتقبل دعائي وصيامي وصلاتي، يا حي يا قيوم! اللَّهُمَّ لا تردني خائبًا، برحمتك يا أرحم الراحمين.",
  "وصلوات الله وسلامه على خاتم النبيين، وسيد الأولين والآخرين، نبينا محمد، وعلى آله الطيبين الطاهرين، وعلى صحابته الغر الميامين، وعلى التابعين، ومن تبعهم بإحسان إلى يوم الدين .",
];

const FATAWA = `سئل الشيخ ابن باز رحمه الله :\nهل هناك دعاء معين لختم القرآن ؟\nفأجاب :\nلم يرد دليل على تعيين دعاء معين فيما نعلم ، ولذلك يجوز للإنسان أن يدعو بما شاء ويتخير من الأدعية النافعة كطلب مغفرة الذنوب والفوز بالجنة والنجاة من النار، والاستعاذة من الفتن وطلب التوفيق لفهم القرآن الكريم على الوجه الذي يرضي الله سبحانه وتعالى ، والعمل به وحفظه ، ونحو ذلك لأنه ثبت عن أنس رضي الله عنه أنه كان يجمع أهله عند ختم القرآن ويدعو ، أما النبي صلى الله عليه وسلم فلم يرد عنه شيء في ذلك فيما أعلم . مجموع فتاوى ابن باز (11/358)`;

const ExclamationIcon = ({ onClick }: { onClick?: () => void }) => (
  <span
    onClick={onClick}
    className="inline-flex items-center justify-center w-7 h-7 rounded-full bg-yellow-500 text-white text-lg font-bold ml-2 border border-yellow-600 cursor-pointer"
    title="ملاحظة مهمة"
  >
    !
  </span>
);

const DuaKhatmPage = ({ onBack }: { onBack: () => void }) => {
  const fatawaRef = useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const scrollToFatawa = () => {
    fatawaRef.current?.scrollIntoView({ behavior: "smooth", block: "center" });
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <div className="flex items-center p-4 border-b border-gray-800">
        <button onClick={onBack} className="text-white text-lg font-bold mr-2">←</button>
        <h2 className="text-xl font-bold flex-1 text-center">دعاء ختم القرآن</h2>
        <ExclamationIcon onClick={scrollToFatawa} />
      </div>
      <div className="p-4 space-y-4 pb-20">
        {DUA_LIST.map((dua, idx) => (
          <div key={idx} className="bg-gray-800 rounded-lg p-4 text-right text-lg leading-loose shadow">
            {dua}
          </div>
        ))}
        <div ref={fatawaRef} className="bg-yellow-100 text-yellow-900 border-2 border-yellow-400 rounded-lg p-4 text-right text-lg leading-loose shadow-lg font-bold">
          {FATAWA.split("\n").map((line, i) => (
            <div key={i}>{line}</div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default DuaKhatmPage; 