Stack trace:
Frame         Function      Args
0007FFFFA4E0  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFF93E0) msys-2.0.dll+0x1FEBA
0007FFFFA4E0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA7B8) msys-2.0.dll+0x67F9
0007FFFFA4E0  000210046832 (000210285FF9, 0007FFFFA398, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFA4E0  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFA4E0  0002100690B4 (0007FFFFA4F0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFA7C0  00021006A49D (0007FFFFA4F0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFFBB8F0000 ntdll.dll
7FFFBA750000 KERNEL32.DLL
7FFFB8F70000 KERNELBASE.dll
7FFFBA820000 USER32.dll
7FFFB8F40000 win32u.dll
000210040000 msys-2.0.dll
7FFFB9900000 GDI32.dll
7FFFB9520000 gdi32full.dll
7FFFB8D30000 msvcp_win.dll
7FFFB9380000 ucrtbase.dll
7FFFB9ED0000 advapi32.dll
7FFFB9BB0000 msvcrt.dll
7FFFB9E00000 sechost.dll
7FFFB9C70000 RPCRT4.dll
7FFFB8560000 CRYPTBASE.DLL
7FFFB94A0000 bcryptPrimitives.dll
7FFFBB7F0000 IMM32.DLL
