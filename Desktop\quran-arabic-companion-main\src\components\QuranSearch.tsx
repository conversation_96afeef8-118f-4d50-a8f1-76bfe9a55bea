import React, { useState, useMemo } from 'react';
import ayahData from '../data/ayahobject.json';
import surahsInfo from '../data/surah.json';

// دالة لتظليل الكلمة المطابقة
function highlightMatch(text: string, query: string) {
  if (!query) return text;
  const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
  return text.split(regex).map((part, i) =>
    regex.test(part) ? <span key={i} className="bg-yellow-400 text-black rounded px-1">{part}</span> : part
  );
}

const QuranSearch: React.FC = () => {
  const [query, setQuery] = useState('');
  const [searching, setSearching] = useState(false);

  // البحث في الآيات
  const results = useMemo(() => {
    if (!query.trim()) return [];
    setSearching(true);
    const q = query.trim();
    // ابحث في النص العثماني أو النص العادي
    const matches = (ayahData as any[]).filter(
      ayah => ayah.uthmaniText?.includes(q) || ayah.text?.includes(q)
    ).slice(0, 50); // حد أقصى للنتائج
    setSearching(false);
    return matches;
  }, [query]);

  // جلب اسم السورة من بيانات السور
  const getSurahName = (surahNumber: number) => {
    const surah = surahsInfo.find(s => parseInt(s.index) === surahNumber);
    return surah ? surah.titleAr : '';
  };

  return (
    <div className="w-full max-w-lg mx-auto p-4">
      <h2 className="text-xl font-bold mb-2 text-center">بحث في نص القرآن</h2>
      <input
        type="text"
        className="w-full p-3 rounded-xl border border-gray-300 focus:outline-none focus:ring-2 focus:ring-green-500 text-lg mb-4 text-right"
        placeholder="اكتب كلمة أو جملة..."
        value={query}
        onChange={e => setQuery(e.target.value)}
        dir="rtl"
        autoFocus
      />
      {searching && <div className="text-center text-gray-500">جاري البحث...</div>}
      {query && results.length === 0 && !searching && (
        <div className="text-center text-gray-400 mt-8">لا توجد نتائج مطابقة.</div>
      )}
      <div className="space-y-4">
        {results.map((ayah, idx) => (
          <div key={idx} className="bg-gray-800 rounded-xl p-4 text-right shadow border border-gray-700">
            <div className="mb-2 text-sm text-gray-300">
              <span className="font-bold">{getSurahName(ayah.surah.number || ayah.surah)}</span>
              <span className="mx-2">-</span>
              <span>الآية {ayah.numberInSurah || ayah.ayah}</span>
              <span className="mx-2">-</span>
              <span>صفحة {ayah.page}</span>
            </div>
            <div className="text-lg leading-loose" style={{ direction: 'rtl' }}>
              {highlightMatch(ayah.uthmaniText || ayah.text, query)}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default QuranSearch; 