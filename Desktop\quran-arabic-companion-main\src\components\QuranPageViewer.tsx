import React, { useState, useEffect, useRef } from 'react'; // Added useRef
import { useSwipeable } from 'react-swipeable';
import { Moon, Sun, Home, Settings, Bookmark, BookOpen, Pencil, Share2, Play, ArrowLeft } from 'lucide-react'; // Added new icons
import surahsInfo from '../data/surah.json';
import ayahData from '../data/ayahobject.json';
import surahData from '../data/surah.json';
import { useQuranSettings } from '../hooks/useQuranSettings';
import { useBookmarks } from '../hooks/useBookmarks';
import { AyahBookmark } from '../hooks/useBookmarks';
import { useWebAudioPlayer } from '../hooks/useWebAudioPlayer';
import { useAudioPlayer } from '../hooks/useAudioPlayer';
import PlaySettingsOverlay from './PlaySettingsOverlay';
import PageSliderOverlay from './PageSliderOverlay';
import AudioPlayerOverlay from './AudioPlayerOverlay';
import imageIndex from '../data/imageIndex.json';

interface Ayah { // Re-added Ayah interface
  number: number;
  uthmaniText: string;
  text: string;
  surah: {
    number: number;
    name: string;
    englishName: string;
    englishNameTranslation: string;
    numberOfAyahs: number;
    revelationType: string;
  };
  numberInSurah: number;
  numberInSurahNative: string;
  juz: number;
  manzil: number;
  page: number;
  ruku: number;
  hizbQuarter: number;
  sajda: boolean;
}

interface QuranPageViewerProps {
  pageNumber: number;
  onBack: () => void;
  onPageChange: (newPage: number) => void;
  totalPages?: number;
  surahName?: string;
  juzNumber?: number;
  onShowSettings?: () => void;
  targetAyahInfo?: { page: number, surah: number, ayah: number } | null;
  onShowPlaySettings?: () => void;
  showPlaySettings?: boolean;
  setShowPlaySettings?: React.Dispatch<React.SetStateAction<boolean>>;
  webAudioCurrentAyah?: { surah: string, ayah: number } | null;
  audioPlayerSettings?: {
    autoScroll?: boolean;
    selectedReciter?: { id: string, name: string, folder: string, image: string };
    fromSurah?: string;
    toSurah?: string;
    fromAyah?: number;
    toAyah?: number;
  };
  audioPlayer: ReturnType<typeof useAudioPlayer>; // أضف هذا السطر
  onClearTargetAyahInfo?: () => void; // دالة اختيارية لتصفير targetAyahInfo من الأعلى
}

const QuranPageViewer: React.FC<QuranPageViewerProps> = ({
  pageNumber,
  onBack,
  onPageChange,
  totalPages = 604,
  surahName,
  juzNumber,
  onShowSettings,
  targetAyahInfo,
  onShowPlaySettings,
  showPlaySettings = false,
  setShowPlaySettings,
  webAudioCurrentAyah,
  audioPlayerSettings,
  audioPlayer,
  onClearTargetAyahInfo // أضف هذا السطر
}) => {
  const [pageData, setPageData] = useState<any>(null);
  const [pageAyahs, setPageAyahs] = useState<Ayah[]>([]);
  const [selectedAyah, setSelectedAyah] = useState<Ayah | null>(null); // State for selected ayah
  const [showColorPicker, setShowColorPicker] = useState(false); // State to show/hide color picker
  const [pickerPosition, setPickerPosition] = useState({ x: 0, y: 0 }); // Position of the color picker
  const pageContentRef = useRef<HTMLDivElement>(null); // Ref for page content to handle clicks outside
  const ayahActionBarRef = useRef<HTMLDivElement>(null); // Ref for the action bar
  const [highlightedAyahId, setHighlightedAyahId] = useState<string | null>(null);
  const [showSlider, setShowSlider] = useState(false);
  const [showAudioPlayer, setShowAudioPlayer] = useState(false);
  const [currentPlayingAyah, setCurrentPlayingAyah] = useState<number | null>(null);

  // استخدام hook الصوت الجديد (Web Audio API)
  const webAudio = useWebAudioPlayer();

  // دالة مساعدة لتحويل اسم السورة إلى رقم
  const getSurahNumberFromName = (surahName: string): number => {
    const surahMap: Record<string, number> = {
      'الفاتحة': 1, 'البقرة': 2, 'آل عمران': 3, 'النساء': 4, 'المائدة': 5, 'الأنعام': 6, 'الأعراف': 7, 'الأنفال': 8, 'التوبة': 9, 'يونس': 10, 'هود': 11, 'يوسف': 12, 'الرعد': 13, 'إبراهيم': 14, 'الحجر': 15, 'النحل': 16, 'الإسراء': 17, 'الكهف': 18, 'مريم': 19, 'طه': 20, 'الأنبياء': 21, 'الحج': 22, 'المؤمنون': 23, 'النور': 24, 'الفرقان': 25, 'الشعراء': 26, 'النمل': 27, 'القصص': 28, 'العنكبوت': 29, 'الروم': 30, 'لقمان': 31, 'السجدة': 32, 'الأحزاب': 33, 'سبأ': 34, 'فاطر': 35, 'يس': 36, 'الصافات': 37, 'ص': 38, 'الزمر': 39, 'غافر': 40, 'فصلت': 41, 'الشورى': 42, 'الزخرف': 43, 'الدخان': 44, 'الجاثية': 45, 'الأحقاف': 46, 'محمد': 47, 'الفتح': 48, 'الحجرات': 49, 'ق': 50, 'الذاريات': 51, 'الطور': 52, 'النجم': 53, 'القمر': 54, 'الرحمن': 55, 'الواقعة': 56, 'الحديد': 57, 'المجادلة': 58, 'الحشر': 59, 'الممتحنة': 60, 'الصف': 61, 'الجمعة': 62, 'المنافقون': 63, 'التغابن': 64, 'الطلاق': 65, 'التحريم': 66, 'الملك': 67, 'القلم': 68, 'الحاقة': 69, 'المعارج': 70, 'نوح': 71, 'الجن': 72, 'المزمل': 73, 'المدثر': 74, 'القيامة': 75, 'الانسان': 76, 'المرسلات': 77, 'النبأ': 78, 'النازعات': 79, 'عبس': 80, 'التكوير': 81, 'الانفطار': 82, 'المطففين': 83, 'الانشقاق': 84, 'البروج': 85, 'الطارق': 86, 'الأعلى': 87, 'الغاشية': 88, 'الفجر': 89, 'البلد': 90, 'الشمس': 91, 'الليل': 92, 'الضحى': 93, 'الشرح': 94, 'التين': 95, 'العلق': 96, 'القدر': 97, 'البينة': 98, 'الزلزلة': 99, 'العاديات': 100, 'القارعة': 101, 'التكاثر': 102, 'العصر': 103, 'الهمزة': 104, 'الفيل': 105, 'قريش': 106, 'الماعون': 107, 'الكوثر': 108, 'الكافرون': 109, 'النصر': 110, 'المسد': 111, 'الإخلاص': 112, 'الفلق': 113, 'الناس': 114
    };
    if (!isNaN(Number(surahName))) {
      return Number(surahName);
    }
    return surahMap[surahName] || 1;
  };

  // دالة مساعدة لتحويل رقم السورة إلى اسم
  const getSurahNameFromNumber = (surahNumber: number): string => {
    const surahMap: Record<number, string> = {
      1: 'الفاتحة', 2: 'البقرة', 3: 'آل عمران', 4: 'النساء', 5: 'المائدة', 6: 'الأنعام', 7: 'الأعراف', 8: 'الأنفال', 9: 'التوبة', 10: 'يونس', 11: 'هود', 12: 'يوسف', 13: 'الرعد', 14: 'إبراهيم', 15: 'الحجر', 16: 'النحل', 17: 'الإسراء', 18: 'الكهف', 19: 'مريم', 20: 'طه', 21: 'الأنبياء', 22: 'الحج', 23: 'المؤمنون', 24: 'النور', 25: 'الفرقان', 26: 'الشعراء', 27: 'النمل', 28: 'القصص', 29: 'العنكبوت', 30: 'الروم', 31: 'لقمان', 32: 'السجدة', 33: 'الأحزاب', 34: 'سبأ', 35: 'فاطر', 36: 'يس', 37: 'الصافات', 38: 'ص', 39: 'الزمر', 40: 'غافر', 41: 'فصلت', 42: 'الشورى', 43: 'الزخرف', 44: 'الدخان', 45: 'الجاثية', 46: 'الأحقاف', 47: 'محمد', 48: 'الفتح', 49: 'الحجرات', 50: 'ق', 51: 'الذاريات', 52: 'الطور', 53: 'النجم', 54: 'القمر', 55: 'الرحمن', 56: 'الواقعة', 57: 'الحديد', 58: 'المجادلة', 59: 'الحشر', 60: 'الممتحنة', 61: 'الصف', 62: 'الجمعة', 63: 'المنافقون', 64: 'التغابن', 65: 'الطلاق', 66: 'التحريم', 67: 'الملك', 68: 'القلم', 69: 'الحاقة', 70: 'المعارج', 71: 'نوح', 72: 'الجن', 73: 'المزمل', 74: 'المدثر', 75: 'القيامة', 76: 'الانسان', 77: 'المرسلات', 78: 'النبأ', 79: 'النازعات', 80: 'عبس', 81: 'التكوير', 82: 'الانفطار', 83: 'المطففين', 84: 'الانشقاق', 85: 'البروج', 86: 'الطارق', 87: 'الأعلى', 88: 'الغاشية', 89: 'الفجر', 90: 'البلد', 91: 'الشمس', 92: 'الليل', 93: 'الضحى', 94: 'الشرح', 95: 'التين', 96: 'العلق', 97: 'القدر', 98: 'البينة', 99: 'الزلزلة', 100: 'العاديات', 101: 'القارعة', 102: 'التكاثر', 103: 'العصر', 104: 'الهمزة', 105: 'الفيل', 106: 'قريش', 107: 'الماعون', 108: 'الكوثر', 109: 'الكافرون', 110: 'النصر', 111: 'المسد', 112: 'الإخلاص', 113: 'الفلق', 114: 'الناس'
    };
    return surahMap[surahNumber] || 'الفاتحة';
  };

  // استخدام الإعدادات
  const {
    settings,
    updateDisplayMode,
    updateFontSize,
    updateColorTheme,
    updateMushafType,
    updateTextAlignment,
    updateNumberFormat,
    toggleSurahNames,
    toggleJuzInfo,
    resetSettings,
    getBackgroundColorClass
  } = useQuranSettings();

  const { bookmarks, togglePageBookmark, isPageBookmarked, toggleAyahBookmark, isAyahBookmarked } = useBookmarks(); // Destructure bookmarks

  // استخدام إعدادات الصوتيات من useAudioPlayer
  // الافتراضي true ليتم تظليل الآيات أثناء التشغيل إلا إذا تم إغلاق الخيار من الإعدادات
  const autoScrollEnabled = audioPlayerSettings?.autoScroll ?? true;
  console.log('[QuranPageViewer] autoScrollEnabled:', autoScrollEnabled, '| audioPlayerSettings?.autoScroll:', audioPlayerSettings?.autoScroll);

  // تعريف selectedReciter في الأعلى ليكون متاحاً في كل الدوال
  const selectedReciter = audioPlayerSettings?.selectedReciter || { id: 'ar.dossary', name: 'ياسر الدوسري', folder: 'Yasser_Ad-Dussary_128kbps', image: '/reciter-images/yasser-dossari.jpg' };

  // استخدم currentAyah من WebAudio مباشرة للتمييز (برقم السورة) فقط إذا كان التمرير التلقائي مفعل
  const effectiveHighlightedAyah = (autoScrollEnabled && webAudio.state.currentAyah)
    ? (webAudio.state.currentAyah.ayah === 0 // البسملة تكون ayah = 0
        ? { surah: 'basmala', ayah: 0, url: '/assets/basmala.mp3' }
        : {
            surah: webAudio.state.currentAyah.surah, // هذا رقم السورة مباشرة
            ayah: webAudio.state.currentAyah.ayah,
            url: '' // لم يعد هناك دالة getAudioUrlForWebAudio أو AyahAudio.getAudioUrl
          }
      )
    : highlightedAyahId
      ? (() => {
          const match = highlightedAyahId.match(/ayah-(\d+)-(\d+)/);
          if (match) return { surah: Number(match[1]), ayah: Number(match[2]), url: '' };
          return null;
        })()
      : null;

  // دالة للعثور على الصفحة التي تحتوي على آية معينة
  const findPageForAyah = (surahName: string, ayahNumber: number): number => {
    console.log('🔍 البحث عن الصفحة للآية:', surahName, 'آية', ayahNumber);
    const targetSurahNumber = getSurahNumberFromName(surahName);
    const ayahArray = ayahData as any[];
    for (let i = 0; i < ayahArray.length; i++) {
      const ayah = ayahArray[i];
      if (ayah && ayah.surah && ayah.surah.number !== undefined && ayah.numberInSurah !== undefined) {
        console.log(`فحص: سورة ${ayah.surah.number} آية ${ayah.numberInSurah} صفحة ${ayah.page}`);
        if (ayah.surah.number === targetSurahNumber && ayah.numberInSurah === ayahNumber) {
          console.log('✅ تم العثور على الآية في الصفحة:', ayah.page, '| السورة:', ayah.surah.name, '| الآية:', ayah.numberInSurah);
          // اطبع كل الآيات في هذه الصفحة
          const allAyahsInPage = ayahArray.filter(a => a && a.page === ayah.page).map(a => `${a.surah?.name} ${a.numberInSurah}`);
          console.log('📄 جميع الآيات في الصفحة', ayah.page, ':', allAyahsInPage);
          return ayah.page;
        }
      }
    }
    console.log('❌ لم يتم العثور على الآية المطلوبة، البقاء في الصفحة الحالية:', pageNumber);
    return pageNumber; // إرجاع الصفحة الحالية إذا لم توجد
  };

  // استقبال scrollToCurrentAyah: انتقال للصفحة أو تمييز فقط
  useEffect(() => {
    const handleScrollToCurrentAyah = (event: CustomEvent) => {
      const { ayah, surah, surahNumber } = event.detail;
      const actualSurahNumber = surahNumber || (surah ? getSurahNumberFromName(surah) : null);
      console.log('[LOG] [scrollToCurrentAyah] event.detail:', event.detail, '| actualSurahNumber:', actualSurahNumber);
      if (!actualSurahNumber) return;
      const ayahId = `ayah-${actualSurahNumber}-${ayah}`;
      // تحقق هل الآية في الصفحة الحالية
      const ayahInPage = pageAyahs.some(a => a.surah.number === actualSurahNumber && a.numberInSurah === ayah);
      console.log('[LOG] [scrollToCurrentAyah] ayahInPage:', ayahInPage, '| pageNumber:', pageNumber);
      if (!ayahInPage) {
        // انتقل للصفحة الصحيحة فقط
        const targetPage = findPageForAyah(actualSurahNumber, ayah);
        console.log('[LOG] [scrollToCurrentAyah] Not in page, targetPage:', targetPage);
        if (targetPage && targetPage !== pageNumber) {
          onPageChange(targetPage);
        }
        return;
      }
      // إذا كانت في الصفحة الحالية، عيّن highlightedAyahId فقط
      setHighlightedAyahId(ayahId);
      console.log('ℹ️ [scrollToCurrentAyah] setHighlightedAyahId:', ayahId, '| سياق: حدث scrollToCurrentAyah');
    };
    window.addEventListener('scrollToCurrentAyah', handleScrollToCurrentAyah as EventListener);
    return () => {
      window.removeEventListener('scrollToCurrentAyah', handleScrollToCurrentAyah as EventListener);
    };
  }, [pageNumber, pageAyahs, onPageChange, findPageForAyah]);

  // عدل useEffect الخاص بالتمييز:
  useEffect(() => {
    console.log('[LOG] [useEffect-highlight] triggered | highlightedAyahId:', highlightedAyahId, '| autoScrollEnabled:', autoScrollEnabled);
    if (!highlightedAyahId) return;
    if (!autoScrollEnabled) {
      console.log('❌ [useEffect] التمرير التلقائي مغلق، لن يتم تظليل الآية | highlightedAyahId:', highlightedAyahId);
      return;
    }
    setTimeout(() => {
      const el = document.getElementById(highlightedAyahId);
      if (el) {
        el.scrollIntoView({ behavior: 'smooth', block: 'center' });
        console.log('✅ [useEffect] تم تظليل وتمرير الآية:', highlightedAyahId);
      } else {
        console.log('⚠️ [useEffect] لم يتم العثور على العنصر للتمييز', highlightedAyahId);
      }
    }, 200);
  }, [highlightedAyahId, pageAyahs, autoScrollEnabled, audioPlayerSettings]);

  useEffect(() => {
    console.log('[LOG] [useEffect-autoScrollEnabled] triggered | autoScrollEnabled:', autoScrollEnabled, '| webAudio.state.currentAyah:', webAudio.state.currentAyah);
    if (!autoScrollEnabled) return;
    // إذا تم تفعيل autoScroll وكان هناك تشغيل صوتي نشط، أعد تظليل الآية الحالية
    if (webAudio.state.currentAyah && typeof webAudio.state.currentAyah.ayah === 'number') {
      console.log('🔄 [autoScroll] تم تفعيل التمرير التلقائي، إعادة تظليل الآية الحالية:', webAudio.state.currentAyah);
      scrollToAyah(webAudio.state.currentAyah.ayah, webAudio.state.currentAyah.surah);
    }
  }, [autoScrollEnabled]);

  useEffect(() => {
    console.log('[LOG] [useEffect-autoScrollEnabled-off] triggered | autoScrollEnabled:', autoScrollEnabled);
    if (autoScrollEnabled) return;
    // عند إغلاق التمرير التلقائي، أزل كل التظليل فورًا
    console.log('🚫 [autoScroll] تم إغلاق التمرير التلقائي، تمت إزالة كل التظليل من الصفحة');
  }, [autoScrollEnabled]);

  useEffect(() => {
    console.log('[LOG] [useEffect-autoScrollEnabled+currentAyah] triggered | autoScrollEnabled:', autoScrollEnabled, '| webAudio.state.currentAyah:', webAudio.state.currentAyah);
    if (autoScrollEnabled && webAudio.state.currentAyah && typeof webAudio.state.currentAyah.ayah === 'number') {
      console.log('🔄 [autoScroll] تفعيل التمرير أو تغير الآية، تظليل الآية الحالية:', webAudio.state.currentAyah);
      scrollToAyah(webAudio.state.currentAyah.ayah, webAudio.state.currentAyah.surah);
    } else if (!autoScrollEnabled) {
      console.log('🚫 [autoScroll] تم إغلاق التمرير التلقائي، تمت إزالة كل التظليل من الصفحة');
    }
  }, [autoScrollEnabled, webAudio.state.currentAyah]);

  // تحديد الوضع الداكن بناءً على الإعدادات
  const isDarkMode = settings.displayMode === 'dark' ||
    (settings.displayMode === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches);

  // تحديد الألوان بناءً على الثيم
  const getThemeColors = () => {
    const baseColors = {
      red: isDarkMode ? '#ef4444' : '#dc2626',
      gray: isDarkMode ? '#6b7280' : '#4b5563',
      blue: isDarkMode ? '#3b82f6' : '#2563eb',
      green: isDarkMode ? '#10b981' : '#059669',
      dark: isDarkMode ? '#ffffff' : '#000000'
    };

    return {
      primary: baseColors[settings.colorTheme],
      text: isDarkMode ? '#ffffff' : '#000000',
      background: isDarkMode ? '#111827' : '#ffffff',
      border: isDarkMode ? '#374151' : '#d1d5db'
    };
  };

  const themeColors = getThemeColors();

  // Load page ayahs from ayahobject.json
  useEffect(() => {
    const loadPageAyahs = () => {
      try {
        const ayahs = (ayahData as Ayah[]).filter((ayah) => ayah.page === pageNumber);
        setPageAyahs(ayahs);

        // Set page data based on first ayah
        if (ayahs.length > 0) {
          const firstAyah = ayahs[0];

          // جمع جميع السور في الصفحة
          const surahsInPage = new Map();
          ayahs.forEach(ayah => {
            const surahNumber = ayah.surah.number;
            if (!surahsInPage.has(surahNumber)) {
              surahsInPage.set(surahNumber, {
                surah: surahNumber,
                name: ayah.surah.name,
                verses: []
              });
            }
            surahsInPage.get(surahNumber).verses.push(ayah.numberInSurah);
          });

          setPageData({
            page: pageNumber,
            juz: firstAyah.juz,
            surahs: Array.from(surahsInPage.values())
          });
        }
      } catch (error) {
        console.error("Failed to load page ayahs", error);
      }
    };
    loadPageAyahs();
  }, [pageNumber]);

  // Scroll to target ayah if provided
  useEffect(() => {
    if (
      targetAyahInfo &&
      targetAyahInfo.page === pageNumber &&
      !highlightedAyahId // فقط إذا لم يكن هناك تظليل حالي
    ) {
      const el = document.getElementById(`ayah-${targetAyahInfo.surah}-${targetAyahInfo.ayah}`);
      if (el) {
        el.scrollIntoView({ behavior: 'smooth', block: 'center' });
        setHighlightedAyahId(`ayah-${targetAyahInfo.surah}-${targetAyahInfo.ayah}`);
        console.log('ℹ️ [targetAyahInfo] setHighlightedAyahId:', `ayah-${targetAyahInfo.surah}-${targetAyahInfo.ayah}`, '| سياق: بحث أو تنقل يدوي');
      }
    }
  }, [targetAyahInfo, pageNumber, pageAyahs, highlightedAyahId]);

  // أضف useEffect موحد لإزالة التظليل اليدوي عند الضغط خارج الآية أو شريط الفواصل/البحث
  useEffect(() => {
    const handleClick = (e: MouseEvent) => {
      // إذا كان الضغط داخل شريط الفواصل أو نافذة منبثقة، لا تزيل التظليل
      const target = e.target as HTMLElement;
      if (
        target.closest('.ayah-action-bar') ||
        target.closest('.search-bar') ||
        target.closest('.bookmark-bar') ||
        target.closest('button') ||
        target.closest('input') ||
        target.tagName === 'BUTTON' ||
        target.tagName === 'INPUT'
      ) {
        return;
      }
      console.log('[CLICK HANDLER] ضغط في أي مكان في الصفحة - فتح/إغلاق الشريط وإزالة التظليل');

      // فتح/إغلاق شريط التنقل السريع
      setShowSlider((prev) => !prev);

      // إزالة التظليل
      setHighlightedAyahId(null);
      setSelectedAyah(null);
      setShowColorPicker(false);
      if (onClearTargetAyahInfo) onClearTargetAyahInfo();

      // امسح التظليل الصوتي فقط إذا كان الصوت متوقف
      if (!webAudio.state.isPlaying && webAudio.clearCurrentAyah) {
        console.log('[REMOVE HIGHLIGHT] مسح التظليل الصوتي لأن الصوت متوقف');
        webAudio.clearCurrentAyah();
      } else if (webAudio.state.isPlaying) {
        console.log('[REMOVE HIGHLIGHT] الصوت شغال - التظليل الصوتي محمي');
      }
    };
    document.addEventListener('mousedown', handleClick);
    return () => document.removeEventListener('mousedown', handleClick);
  }, [onClearTargetAyahInfo]);

  // Swipe handlers - تغيير اتجاه السحب
  const handlers = useSwipeable({
    onSwipedRight: () => pageNumber < totalPages && onPageChange(pageNumber + 1), // يمين = صفحة جديدة
    onSwipedLeft: () => pageNumber > 1 && onPageChange(pageNumber - 1), // شمال = صفحة سابقة
    trackMouse: true,
  });

  // Get all surah names from page data
  const getSurahNames = () => {
    if (!pageData || !pageData.surahs || pageData.surahs.length === 0) return '';

    const surahNames = pageData.surahs.map(surahData => {
      const surahInfo = surahsInfo.find(s => parseInt(s.index) === surahData.surah);
      return surahInfo ? `سورة ${surahInfo.titleAr}` : '';
    }).filter(name => name !== '');

    // إذا كان هناك أكثر من سورة، اربطهم بـ " - "
    return surahNames.join(' - ');
  };

  // Get juz name
  const getJuzName = () => {
    if (!pageData) return '';
    const juzNumber = settings.numberFormat === 'arabic' ? convertToArabicNumbers(pageData.juz) : pageData.juz.toString();
    return pageData.juz ? `الجزء ${juzNumber}` : '';
  };

  // Convert numbers to Arabic
  const convertToArabicNumbers = (num: number) => {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return num.toString().split('').map(digit =>
      /\d/.test(digit) ? arabicNumbers[parseInt(digit)] : digit
    ).join('');
  };

  // حساب معلومات الحزب من hizbQuarter
  const getHizbInfo = () => {
    if (!pageAyahs.length) return null;

    // البحث عن أول ربع حزب جديد في الصفحة
    let hizbQuarter = pageAyahs[0].hizbQuarter;

    // التحقق من وجود بداية حزب جديد في الصفحة
    for (const ayah of pageAyahs) {
      const currentQuarter = ayah.hizbQuarter;
      const quarterInHizb = ((currentQuarter - 1) % 4) + 1;

      // إذا وجدنا الربع الأول من حزب جديد، استخدمه
      if (quarterInHizb === 1) {
        hizbQuarter = currentQuarter;
        break;
      }
    }

    // حساب رقم الحزب (كل حزب = 4 أرباع)
    const hizbNumber = Math.ceil(hizbQuarter / 4);

    // حساب ربع الحزب (1-4)
    const quarterInHizb = ((hizbQuarter - 1) % 4) + 1;

    return {
      hizbNumber,
      quarterInHizb,
      hizbQuarter
    };
  };

  // التحقق من وجود بداية حزب جديد في الصفحة (للاستخدام المستقبلي)
  const hasNewHizbStart = () => {
    if (!pageAyahs.length) return false;

    const hizbInfo = getHizbInfo();
    if (!hizbInfo) return false;

    // إذا كان الربع الأول من الحزب، فهذا يعني بداية حزب جديد
    return hizbInfo.quarterInHizb === 1;
  };

  // Component for verse number - Always original Mushaf style
  const VerseNumber: React.FC<{ number: number; ayahBookmarkColor?: string }> = ({ number, ayahBookmarkColor }) => {
    const displayNumber = settings.numberFormat === 'arabic' ? convertToArabicNumbers(number) : number.toString();

    return (
      <span
        className="inline-block mx-2"
        style={{
          fontFamily: 'UthmanicHafs, Amiri, serif',
          fontWeight: 'normal',
          color: ayahBookmarkColor || themeColors.primary, // Apply bookmark color if exists
        }}
      >
        {displayNumber}
      </span>
    );
  };

  // Handle long press/right click on ayah
  const handleAyahContextMenu = (event: React.MouseEvent, ayah: Ayah) => {
    event.preventDefault();
    setSelectedAyah(ayah);
    setShowColorPicker(true);
    const x = event.clientX;
    const y = event.clientY;
    setTimeout(() => {
      const bar = ayahActionBarRef.current;
      if (bar) {
        const rect = bar.getBoundingClientRect();
        let newX = x;
        let newY = y;
        // احسب الموضع النهائي بعد التحويل (translate(-50%, -120%))
        let left = newX - rect.width / 2;
        let top = newY - rect.height * 1.2;
        // عدل x إذا خرج الشريط عن اليسار أو اليمين
        if (left < 8) {
          newX = 8 + rect.width / 2;
          left = 8;
        }
        if (left + rect.width > window.innerWidth - 8) {
          newX = window.innerWidth - 8 - rect.width / 2;
          left = window.innerWidth - 8 - rect.width;
        }
        // عدل y إذا خرج الشريط عن الأعلى أو الأسفل
        if (top < 8) {
          newY = 8 + rect.height * 1.2;
          top = 8;
        }
        if (top + rect.height > window.innerHeight - 8) {
          newY = window.innerHeight - 8 - rect.height * 0.2;
          top = window.innerHeight - 8 - rect.height;
        }
        setPickerPosition({ x: newX, y: newY });
      }
    }, 0);
    setPickerPosition({ x, y });
  };

  // Handle color selection for ayah bookmark
  const handleColorSelect = (color: string) => {
    if (selectedAyah) {
      toggleAyahBookmark(selectedAyah.surah.number, selectedAyah.numberInSurah, color);
      // Keep selectedAyah and showColorPicker true to allow adding multiple bookmarks
      // The user can click outside to close it
    }
  };

  // Close color picker if clicked outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const actionBar = ayahActionBarRef.current;
      if (actionBar && !actionBar.contains(event.target as Node)) {
        setShowColorPicker(false);
        setSelectedAyah(null);
      }
    };
    if (showColorPicker) {
      document.addEventListener('mousedown', handleClickOutside);
    } else {
      document.removeEventListener('mousedown', handleClickOutside);
    }
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showColorPicker]);

  // استبدل تعريف AyahActionBar ليأخذ selectedAyah كـ prop
  const AyahActionBar: React.FC<{ onColorSelect: (color: string) => void; ayahBookmarks: AyahBookmark[], selectedAyah: Ayah | null, onShowPlaySettings: () => void }> = ({ onColorSelect, ayahBookmarks, selectedAyah, onShowPlaySettings }) => {
    const colors = ['red', 'blue', 'green', 'yellow', 'purple', 'orange'];
    // إضافة حالة لإظهار إعدادات البلاي
    // احذف useState(showPlaySettings) من هنا
    // إضافة حالة لتشغيل الصوت
    // جلب الشيخ المختار من الإعدادات أو hook الصوتيات
    // الحالة الصحيحة للقائمة المنبثقة
    const [showPlayMenu, setShowPlayMenu] = useState(false);
    const playMenuRef = useRef<HTMLDivElement>(null);

    // دالة لتشغيل الآية باستخدام نظام الصوت الرئيسي
    const playAyah = () => {
      if (!selectedAyah) return;
      const reciter = audioPlayerSettings?.selectedReciter;
      const moshaf = reciter?.moshafObj;
      if (!reciter || !moshaf) {
        alert('يرجى اختيار القارئ والرواية أولاً');
        return;
      }
      webAudio.playAyah(
        Number(selectedAyah.surah.number),
        Number(selectedAyah.numberInSurah),
        reciter,
        moshaf
      );
    };

    // دالة لتشغيل الآية من نقطة البداية
    const playFromHere = () => {
      const reciter = audioPlayerSettings?.selectedReciter;
      const moshaf = reciter?.moshafObj;
      if (!reciter || !moshaf) {
        alert('يرجى اختيار القارئ والرواية أولاً');
        return;
      }
      const ayahList = pageAyahs.map(ayah => ({
        surah: Number(ayah.surah.number),
        ayah: Number(ayah.numberInSurah)
      }));
      if (ayahList.length > 0) {
        webAudio.playAyahList(ayahList, reciter, moshaf, 0);
      }
    };

    // تعديل منطق إغلاق الشريط ليشمل القائمة المنبثقة
    useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        const actionBar = ayahActionBarRef.current;
        const playMenu = playMenuRef.current;
        if (
          actionBar &&
          !actionBar.contains(event.target as Node) &&
          (!playMenu || !playMenu.contains(event.target as Node))
        ) {
          setShowColorPicker(false);
          setSelectedAyah(null);
        }
      };
      if (showColorPicker) {
        document.addEventListener('mousedown', handleClickOutside);
      } else {
        document.removeEventListener('mousedown', handleClickOutside);
      }
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }, [showColorPicker]);

    return (
      <div
        ref={ayahActionBarRef}
        className={`ayah-action-bar absolute z-50 p-1 rounded-lg shadow-lg flex items-center gap-1 ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}
        style={{
          top: pickerPosition.y,
          left: pickerPosition.x,
          transform: 'translate(-50%, -120%)',
          boxSizing: 'border-box',
          overflow: 'hidden',
        }}
      >
        {/* Color selection buttons */}
        {colors.map(color => {
          const isCurrentColorBookmarked = ayahBookmarks.some(b => b.color === color);
          return (
            <button
              key={color}
              className={`p-1 rounded-full transition-colors ${
                isCurrentColorBookmarked
                  ? `text-${color}-500 hover:bg-${color}-100 dark:hover:bg-${color}-900`
                  : 'text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700'
              }`}
              onClick={() => onColorSelect(color)}
            >
              <Bookmark size={18} fill={isCurrentColorBookmarked ? color : 'none'} color={color} />
            </button>
          );
        })}
        {/* Other options */}
        <button className="p-1 rounded-full text-gray-300 hover:bg-gray-700">
          <BookOpen size={18} />
        </button>
        <button className="p-1 rounded-full text-gray-300 hover:bg-gray-700">
          <Pencil size={18} />
        </button>
        <button className="p-1 rounded-full text-gray-300 hover:bg-gray-700">
          <Share2 size={18} />
        </button>
        {/* زر تشغيل الآية مع قائمة خيارات */}
        <div className="relative">
          <button
            className="p-1 rounded-full text-green-500 hover:bg-green-100 dark:hover:bg-green-900"
            onClick={() => setShowPlayMenu((prev) => !prev)}
          >
            <Play size={18} />
          </button>
          {showPlayMenu && (
            <div ref={playMenuRef} className="absolute right-0 z-50 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 text-right">
              <button
                className="block w-full px-4 py-2 text-sm text-gray-800 dark:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700"
                onClick={() => {
                  playAyah();
                  setShowPlayMenu(false);
                }}
              >
                تشغيل هذه الآية فقط
              </button>
              <button
                className="block w-full px-4 py-2 text-sm text-gray-800 dark:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700"
                onClick={() => {
                  playFromHere();
                  setShowPlayMenu(false);
                }}
              >
                تشغيل من هنا
              </button>
              <button
                className="block w-full px-4 py-2 text-sm text-gray-800 dark:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700 border-t border-gray-200 dark:border-gray-700"
                onClick={() => {
                  if (setShowPlaySettings) {
                    setShowPlaySettings(true);
                  } else if (onShowPlaySettings) {
                    onShowPlaySettings();
                  }
                }}
              >
                مزيد من الخيارات
              </button>
            </div>
          )}
        </div>
        {/* زر مزيد من خيارات الاستماع */}
        <button className="p-1 rounded-full text-yellow-500 hover:bg-yellow-100 dark:hover:bg-yellow-900" onClick={() => {
          if (setShowPlaySettings) {
            setShowPlaySettings(true);
          } else if (onShowPlaySettings) {
            onShowPlaySettings();
          }
        }}>
          <Settings size={18} />
        </button>
      </div>
    );
  };

  // Enhanced Surah Header component matching reference images
  const SurahHeader: React.FC<{ surahName: string; surahNumber: number }> = ({ surahName, surahNumber }) => {
    // Special handling for Al-Fatiha
    if (surahNumber === 1) {
      return (
        <div className="text-center mb-6">
          {/* Al-Fatiha header with simple border */}
          <div className={`border ${
            isDarkMode ? 'border-gray-600' : 'border-gray-400'
          } rounded-none p-3 mb-4 text-center`}>
            <div className={`text-lg font-normal ${
              isDarkMode ? 'text-white' : 'text-black'
            }`} style={{
              fontFamily: 'UthmanicHafs, Amiri, serif',
              lineHeight: '1.6'
            }}>
              الْفَاتِحَةِ
            </div>
          </div>
          {/* Bismillah for Al-Fatiha */}
          <div className={`text-lg font-normal mb-4 text-center ${
            isDarkMode ? 'text-white' : 'text-black'
          }`} style={{
            fontFamily: 'UthmanicHafs, Amiri, serif',
            lineHeight: '1.6'
          }}>
            بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ
          </div>
        </div>
      );
    }

    // Regular surah headers
    return (
      <div className="text-center mb-6">
        <div className={`border ${
          isDarkMode ? 'border-gray-600' : 'border-gray-400'
        } rounded-none p-3 mb-4 text-center`}>
          <div className={`text-lg font-normal ${
            isDarkMode ? 'text-white' : 'text-black'
          }`} style={{
            fontFamily: 'UthmanicHafs, Amiri, serif',
            lineHeight: '1.6'
          }}>
            {surahName}
          </div>
        </div>
        {/* Bismillah for other surahs */}
        {surahNumber !== 9 && ( // Do not show Bismillah for Surah At-Tawbah (Surah 9)
          <div className={`text-lg font-normal mb-4 text-center ${
            isDarkMode ? 'text-white' : 'text-black'
          }`} style={{
            fontFamily: 'UthmanicHafs, Amiri, serif',
            lineHeight: '1.6'
          }}>
            بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ
          </div>
        )}
      </div>
    );
  };

  // Component for page content
  const PageContent: React.FC = () => {
    if (pageAyahs.length === 0) return null;

    const groupedBySurah = pageAyahs.reduce((acc, ayah) => {
      const surahNumber = ayah.surah.number;
      if (!acc[surahNumber]) {
        acc[surahNumber] = [];
      }
      acc[surahNumber].push(ayah);
      return acc;
    }, {} as Record<number, Ayah[]>);

    return (
      <div className="space-y-8">
        {Object.entries(groupedBySurah).map(([surahNumber, ayahs]) => {
          const firstAyah = ayahs[0];
          const isNewSurah = firstAyah.numberInSurah === 1;

          return (
            <div key={surahNumber} className="space-y-4">
              {isNewSurah && settings.showSurahNames && (
                <SurahHeader
                  surahName={firstAyah.surah.name}
                  surahNumber={parseInt(surahNumber)}
                />
              )}

              <div className={`text-right leading-relaxed ${
                isDarkMode ? 'text-white' : 'text-black'
              }`} style={{
                fontFamily: 'UthmanicHafs, Amiri, serif',
                fontSize: `${settings.fontSize}px`,
                lineHeight: '2.0',
                textAlign: settings.textAlignment === 'justified' ? 'justify' : 'right',
                textJustify: 'inter-word',
                fontWeight: 'normal',
                letterSpacing: '0.005em',
                wordSpacing: '0.03em',
                fontFeatureSettings: '"liga" 1, "dlig" 1, "kern" 1, "mark" 1, "mkmk" 1',
                textRendering: 'optimizeLegibility'
              }}>
                {ayahs.map((ayah, index) => {
                  // Get all bookmarks for this ayah
                  const ayahBookmarks = bookmarks.filter(
                    b => b.type === 'ayah' && b.surah === ayah.surah.number && b.ayah === ayah.numberInSurah
                  ) as AyahBookmark[];

                  // Determine the background color based on the first bookmark, or no color if none
                  const backgroundColor = ayahBookmarks.length > 0 ? ayahBookmarks[0].color + '33' : 'transparent';
                  // Determine the verse number color based on the first bookmark, or default if none
                  const verseNumberColor = ayahBookmarks.length > 0 ? ayahBookmarks[0].color : undefined;

                  // منطق التظليل الموحد
                  const isHighlight = (
                    (selectedAyah && selectedAyah.surah.number === ayah.surah.number && selectedAyah.numberInSurah === ayah.numberInSurah) ||
                    (highlightedAyahId === `ayah-${ayah.surah.number}-${ayah.numberInSurah}`) ||
                    (targetAyahInfo && targetAyahInfo.surah === ayah.surah.number && targetAyahInfo.ayah === ayah.numberInSurah) ||
                    (effectiveHighlightedAyah && ayah.surah.number === effectiveHighlightedAyah.surah && ayah.numberInSurah === effectiveHighlightedAyah.ayah)
                  );
                  return (
                    <span
                      key={ayah.number}
                      id={`ayah-${ayah.surah.number}-${ayah.numberInSurah}`}
                      className={
                        `${settings.mushafType === 'separate' ? 'block mb-2' : 'inline'} relative` +
                        (isHighlight ? ' ayah-highlight' : '')
                      }
                      onContextMenu={(e) => handleAyahContextMenu(e, ayah)}
                      style={{
                        backgroundColor: (!effectiveHighlightedAyah || effectiveHighlightedAyah.surah !== ayah.surah.number || effectiveHighlightedAyah.ayah !== ayah.numberInSurah) ? backgroundColor : undefined,
                        borderRadius: '4px',
                        padding: '2px 4px',
                        margin: '0 -4px',
                        cursor: 'pointer',
                      }}
                    >
                      <span
                        dangerouslySetInnerHTML={{
                          __html: ayah.uthmaniText
                        }}
                      />
                      <VerseNumber number={ayah.numberInSurah} ayahBookmarkColor={verseNumberColor} />
                      {/* Add space between verses - only for connected mushaf */}
                      {settings.mushafType === 'connected' && index < ayahs.length - 1 && <span> </span>}
                    </span>
                  );
                })}
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  const PageFooter: React.FC = () => {
    const hizbInfo = getHizbInfo();
    const pageNumDisplay = settings.numberFormat === 'arabic' ? convertToArabicNumbers(pageNumber) : pageNumber.toString();

    if (!settings.showJuzInfo || !hizbInfo) {
      // Show only page number if juz info is disabled
      return (
        <div className="fixed bottom-4 right-4 z-10">
          <div className="bg-gray-900/70 dark:bg-black/50 backdrop-blur-sm text-white text-xs font-semibold px-4 py-2 rounded-full shadow-lg">
            {pageNumDisplay}
          </div>
        </div>
      );
    }

    const hizbNumberDisplay = settings.numberFormat === 'arabic' ? convertToArabicNumbers(hizbInfo.hizbNumber) : hizbInfo.hizbNumber.toString();
    
    let quarterText = '';
    switch (hizbInfo.quarterInHizb) {
      case 1: quarterText = '١/٤'; break;
      case 2: quarterText = '١/٢'; break;
      case 3: quarterText = '٣/٤'; break;
      case 4: quarterText = ''; break;
    }

    const content = quarterText 
      ? `الحزب ${hizbNumberDisplay} • ${quarterText}`
      : `الحزب ${hizbNumberDisplay}`;
    
    return (
      <div className="fixed bottom-4 right-4 z-10">
        <div className="bg-gray-900/70 dark:bg-black/50 backdrop-blur-sm text-white text-xs font-semibold px-4 py-2 rounded-full shadow-lg flex items-center gap-2">
          <span>{content}</span>
          <span className="text-gray-400">|</span>
          <span>{pageNumDisplay}</span>
        </div>
      </div>
    );
  };

  const backgroundClass = isDarkMode
    ? 'bg-gray-900'
    : 'bg-[#fefefe]';

  const textClass = isDarkMode ? 'text-white' : 'text-black';
  const headerTextClass = isDarkMode ? 'text-white' : 'text-black';

  // دالة لجلب صورة مصغرة للصفحة الحالية
  const getThumbnailUrl = (page: number) => {
    const entry = (imageIndex as any)[page];
    return entry ? entry.url : undefined;
  };

  // استخدم useEffect للتمرير التلقائي عند تغيير effectiveHighlightedAyah أو autoScrollEnabled
  useEffect(() => {
    // تجاهل البسملة في التمرير والتظليل
    if (
      effectiveHighlightedAyah &&
      autoScrollEnabled &&
      effectiveHighlightedAyah.surah !== 'basmala' &&
      effectiveHighlightedAyah.ayah !== 0 &&
      typeof effectiveHighlightedAyah.surah === 'number'
    ) {
      scrollToAyah(effectiveHighlightedAyah.ayah, effectiveHighlightedAyah.surah);
    }
  }, [effectiveHighlightedAyah, autoScrollEnabled]);

  // انتقال تلقائي للصفحة التالية عند تغير الآية الحالية أثناء التشغيل (فقط إذا كان التمرير التلقائي مفعل)
  useEffect(() => {
    if (!autoScrollEnabled || !webAudio.state.currentAyah) return;
    const { surah, ayah } = webAudio.state.currentAyah;
    // ابحث عن الصفحة التي تحتوي على هذه الآية
    const targetPage = findPageForAyah(getSurahNameFromNumber(surah), ayah);
    if (targetPage && targetPage !== pageNumber) {
      onPageChange(targetPage);
    }
  }, [autoScrollEnabled, webAudio.state.currentAyah, pageNumber, onPageChange, findPageForAyah]);

  // Helper to build ayahList with basmala if needed (منسوخة من PlaySettingsOverlay)
  function buildAyahListWithBasmala({fromSurah, toSurah, fromAyah, toAyah, selectedReciter, surahData}) {
    const startIdx = surahData.findIndex(s => s.titleAr === fromSurah || s.title === fromSurah);
    const endIdx = surahData.findIndex(s => s.titleAr === toSurah || s.title === toSurah);
    if (startIdx === -1 || endIdx === -1) return [];
    const ayahList = [];
    const reciterFolder = selectedReciter?.folder;
    if (!reciterFolder) return [];
    for (let s = startIdx; s <= endIdx; s++) {
      const surah = surahData[s];
      const surahNumber = surah.number || surah.id || (s + 1);
      // إذا كانت بداية السورة من الآية 1 وليست التوبة، أضف البسملة أولاً
      let ayahStart = 1;
      let ayahEnd = surah.count;
      if (s === startIdx) ayahStart = fromAyah;
      if (s === endIdx) ayahEnd = toAyah;
      if (ayahStart === 1 && surahNumber !== 9) {
        ayahList.push({
          surah: surahNumber,
          ayah: 0,
          url: `https://www.everyayah.com/data/${reciterFolder}/basmala.mp3`
        });
      }
      for (let a = ayahStart; a <= ayahEnd; a++) {
        const surahStr = surahNumber.toString().padStart(3, '0');
        const ayahStr = a.toString().padStart(3, '0');
        ayahList.push({
          surah: surahNumber,
          ayah: a,
          url: `https://www.everyayah.com/data/${reciterFolder}/${surahStr}${ayahStr}.mp3`
        });
      }
    }
    return ayahList;
  }

  // دالة موحدة لتشغيل المقطع الحالي بنفس منطق زر إعدادات البلاي
  const handlePlaySection = () => {
    if (!webAudio || typeof webAudio.playAyahList !== 'function') return;
    const fromSurah = audioPlayerSettings?.fromSurah || '';
    const toSurah = audioPlayerSettings?.toSurah || '';
    const fromAyah = audioPlayerSettings?.fromAyah || 1;
    const toAyah = audioPlayerSettings?.toAyah || 1;
    const reciter = audioPlayerSettings?.selectedReciter;
    if (!fromSurah || !toSurah || !fromAyah || !toAyah || !reciter) {
      alert('يرجى اختيار القارئ والإعدادات أولاً');
      return;
    }
    const ayahList = buildAyahListWithBasmala({fromSurah, toSurah, fromAyah, toAyah, selectedReciter: reciter, surahData});
    if (ayahList.length === 0) {
      console.warn('No ayahs to play');
      return;
    }
    webAudio.playAyahList(ayahList, reciter, null, 0);
  };

  // Scroll to a specific ayah by number in the current page
  const scrollToAyah = (ayahNumber: number, surahNumberOverride?: number) => {
    console.log('[LOG] [scrollToAyah] called | ayahNumber:', ayahNumber, '| surahNumberOverride:', surahNumberOverride, '| autoScrollEnabled:', autoScrollEnabled);
    if (!autoScrollEnabled) {
      console.log('❌ [scrollToAyah] التمرير التلقائي مغلق، لن يتم تظليل الآية');
      return;
    }
    let surahNumber = surahNumberOverride;
    if (!surahNumber) {
      // Try to get the surah number from the first ayah in the page
      if (pageAyahs && pageAyahs.length > 0) {
        surahNumber = pageAyahs[0].surah.number;
      }
    }
    if (!surahNumber) {
      console.log('[LOG] [scrollToAyah] surahNumber is undefined, aborting');
      return;
    }
    const ayahId = `ayah-${surahNumber}-${ayahNumber}`;
    setHighlightedAyahId(ayahId);
    setTimeout(() => {
      const el = document.getElementById(ayahId);
      if (el) {
        el.scrollIntoView({ behavior: 'smooth', block: 'center' });
        console.log('✅ [scrollToAyah] تم تظليل الآية:', ayahId);
      } else {
        console.log('⚠️ [scrollToAyah] لم يتم العثور على العنصر للتمييز', ayahId);
      }
    }, 200);
  };



  console.log('[QuranPageViewer] render | webAudioCurrentAyah:', webAudioCurrentAyah, '| effectiveHighlightedAyah:', effectiveHighlightedAyah);
  console.log('QuranPageViewer audioPlayerSettings:', audioPlayerSettings);

  return (
    <>
      <style>{`
.ayah-highlight {
  background: rgba(60,60,60,0.13) !important;
  box-shadow: 0 8px 32px 0 rgba(0,0,0,0.12);
  border-radius: 18px;
  transition: background 0.3s, box-shadow 0.3s;
}
`}</style>

      <div className="bg-white dark:bg-gray-900 min-h-screen">
        {/* Header */}
        <div className="sticky top-0 z-20 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm shadow-sm">
          <div className="flex items-center justify-between p-2 h-16">
            {/* Left: Surah Name */}
            <div className="w-1/3 text-right text-sm font-medium text-gray-800 dark:text-gray-200 truncate">
              {settings.showSurahNames && getSurahNames()}
            </div>

            {/* Center: Juz Info */}
            <div className="w-1/3 text-center text-sm text-gray-600 dark:text-gray-400">
              {settings.showJuzInfo && getJuzName()}
            </div>

            {/* Right: Controls */}
            <div className="w-1/3 flex justify-end items-center gap-2">
              {/* Bookmark button for page */}
              <button
                onClick={() => togglePageBookmark(pageNumber)}
                className={`p-2 rounded-full transition-colors ${
                  isPageBookmarked(pageNumber)
                    ? 'text-yellow-500 hover:bg-yellow-100 dark:hover:bg-yellow-900'
                    : 'text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700'
                }`}
              >
                <Bookmark size={20} fill={isPageBookmarked(pageNumber) ? 'currentColor' : 'none'} />
              </button>
              {onShowSettings && (
                <button
                  onClick={onShowSettings}
                  className="p-2 rounded-full text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
                  aria-label="Settings"
                >
                  <Settings size={20} />
                </button>
              )}
              <button
                onClick={onBack}
                className="p-2 rounded-full text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
                aria-label="Back"
              >
                <ArrowLeft size={20} />
              </button>
            </div>
          </div>
        </div>
        <div {...handlers} className="p-4 pb-16" style={{ minHeight: 'calc(100vh - 64px)' }} ref={pageContentRef}>
          <PageContent />
          {showColorPicker && selectedAyah && (
            <AyahActionBar
              onColorSelect={handleColorSelect}
              ayahBookmarks={bookmarks.filter(b => b.type === 'ayah' && b.surah === selectedAyah.surah.number && b.ayah === selectedAyah.numberInSurah) as AyahBookmark[]}
              selectedAyah={selectedAyah}
              onShowPlaySettings={() => {
                if (setShowPlaySettings) {
                  setShowPlaySettings(true);
                } else if (onShowPlaySettings) {
                  onShowPlaySettings();
                }
              }}
            />
          )}
        </div>
        <PageFooter />
      </div>
      {/* شريط التنقل السريع */}
      {showSlider && (
        <div className="page-slider-overlay">
          <PageSliderOverlay
            currentPage={pageNumber}
            totalPages={totalPages}
            surahName={getSurahNames()}
            juzNumber={pageData?.juz || 1}
            onPageChange={(p) => { setShowSlider(false); onPageChange(p); }}
            thumbnailUrl={getThumbnailUrl(pageNumber)}
            onClose={() => setShowSlider(false)}
            isPlaying={webAudio.state.isPlaying}
            onPlayPause={() => {
              if (webAudio.state.isPlaying) {
                webAudio.pause();
              } else {
                handlePlaySection(); // استخدم نفس منطق زر إعدادات البلاي
              }
            }}
            onShowAudioSettings={() => setShowAudioPlayer(true)}
            onShowPlaySettings={() => {
              if (setShowPlaySettings) {
                setShowPlaySettings(true);
              }
            }}
          />
        </div>
      )}
      {/* نافذة إعدادات البلاي */}
      {(() => {
        console.log('🎯 QuranPageViewer render - showPlaySettings:', showPlaySettings);
        return showPlaySettings && setShowPlaySettings && (
          <PlaySettingsOverlay
            open={showPlaySettings}
            onClose={() => {
              console.log('🚪 onClose called - closing PlaySettings');
              setShowPlaySettings(false);
            }}
            webAudio={webAudio}
            audioPlayer={audioPlayer} // صححنا هنا: مرر الـ audioPlayer الصحيح
          />
        );
      })()}
    </>
  );
};

export default QuranPageViewer;