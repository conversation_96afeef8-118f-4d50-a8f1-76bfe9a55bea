import { useState, useEffect, useRef } from "react";
import QuranHeader from "../components/QuranHeader";
import QuranTabs from "../components/QuranTabs";
import SuraCard from "../components/SuraCard";
import JuzCard from "../components/JuzCard";
import QuranReader from "../components/QuranReader";
import PageReader from "../components/PageReader";
import PagesList from "../components/PagesList";
import UnifiedQuranSettings from "../components/UnifiedQuranSettings";
import SettingsTestPage from "../components/SettingsTestPage";
import KhatmaTracker from "../components/KhatmaTracker";
import BookmarksPage from "./BookmarksPage"; // Import BookmarksPage
import AudioDownloadManager from "../components/AudioDownloadManager";
import { suras, ajzaa, quranData } from "../data/quranData";
import { useQuranSettings } from "../hooks/useQuranSettings";
import { Bookmark as BookmarkIcon, Download } from 'lucide-react';
import DuaKhatmPage from "../components/DuaKhatmPage";
import WaqfSignsPage from "../components/WaqfSignsPage";
import QuranSearch from '../components/QuranSearch';
import { CommandDialog } from '../components/ui/command';
import SearchPage from './SearchPage';
import { useWebAudioPlayer } from '../hooks/useWebAudioPlayer';
import { useAudioPlayer } from '../hooks/useAudioPlayer';


// Add PageCard component
const PageCard = ({ pageNumber, onClick }: { pageNumber: number; onClick: () => void }) => {
  return (
    <button
      onClick={onClick}
      className="bg-gray-800 rounded-lg p-3 text-center hover:bg-green-700 transition-colors text-white font-bold flex flex-col items-center justify-center"
    >
      <span className="text-lg">{pageNumber}</span>
      <span className="text-xs text-gray-400 mt-1">صفحة</span>
    </button>
  );
};

const PagesModernTab = ({ selectedPageNumber, setSelectedPageNumber, goToView, setSelectedAyahInfo }: {
  selectedPageNumber: number | null,
  setSelectedPageNumber: (n: number) => void,
  goToView: (v: string) => void,
  setSelectedAyahInfo: (info: any) => void
}) => {
  const [showInfo, setShowInfo] = useState(false);
  const [savedPage, setSavedPage] = useState(() => Number(localStorage.getItem('savedPage')) || null);
  const [inputMode, setInputMode] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const centerPage = selectedPageNumber || 1;
  const [longPressTriggered, setLongPressTriggered] = useState(false);

  useEffect(() => {
    setSavedPage(Number(localStorage.getItem('savedPage')) || null);
  }, [selectedPageNumber]);

  const handleSave = () => {
    if (centerPage) {
      localStorage.setItem('savedPage', String(centerPage));
      setSavedPage(centerPage);
    }
  };

  const handleGoToSaved = () => {
    if (savedPage) {
      setSelectedPageNumber(savedPage);
      goToView('pageReader');
    }
  };

  // عجلة الصفحات
  const getPages = () => {
    const arr = [];
    for (let i = -1; i <= 1; i++) {
      const page = centerPage + i;
      if (page < 1 || page > 604) arr.push(null);
      else arr.push(page);
    }
    return arr;
  };

  const handleWheel = (e: React.WheelEvent) => {
    if (e.deltaY > 0 && centerPage < 604) setSelectedPageNumber(centerPage + 1);
    if (e.deltaY < 0 && centerPage > 1) setSelectedPageNumber(centerPage - 1);
  };

  const handlePageClick = (page: number | null) => {
    if (page && page !== centerPage) setSelectedPageNumber(page);
  };

  return (
    <div className="flex flex-col items-center gap-6 w-full max-w-md mx-auto pt-6">
      {/* عجلة الصفحات */}
      <div
        className="flex flex-col items-center gap-2 w-full select-none"
        onWheel={handleWheel}
      >
        {getPages().map((page, idx) => (
          <div
            key={idx}
            onClick={() => handlePageClick(page)}
            className={
              page === centerPage
                ? "w-full text-center text-2xl font-bold bg-gray-800 text-white rounded-xl py-3 mb-1 mt-1"
                : "w-full text-center text-lg font-normal bg-gray-900 text-gray-400 rounded-xl py-2 cursor-pointer hover:bg-gray-800"
            }
            style={{ opacity: page ? 1 : 0, pointerEvents: page ? 'auto' : 'none' }}
          >
            {page ? `الصفحة ${page}` : ''}
          </div>
        ))}
      </div>
      {/* إدخال رقم الصفحة */}
      {inputMode ? (
        <div className="w-full flex flex-col gap-2 items-center">
          <input
            type="number"
            min={1}
            max={604}
            value={inputValue}
            onChange={e => setInputValue(e.target.value)}
            className="w-full bg-gray-800 text-white rounded-xl p-3 text-center text-lg border border-gray-700 focus:outline-none focus:ring-2 focus:ring-green-500 transition-all"
            placeholder="اكتب رقم الصفحة"
            autoFocus
          />
          <div className="flex w-full gap-2">
            <button
              onClick={() => {
                const val = parseInt(inputValue, 10);
                if (!isNaN(val) && val >= 1 && val <= 604) {
                  setSelectedPageNumber(val);
                  setInputMode(false);
                  setInputValue('');
                }
              }}
              className="flex-1 bg-green-600 hover:bg-green-700 text-white py-3 rounded-xl font-bold text-lg"
            >
              اذهب
            </button>
            <button
              onClick={() => { setInputMode(false); setInputValue(''); }}
              className="flex-1 bg-gray-700 hover:bg-gray-600 text-white py-3 rounded-xl font-bold text-lg"
            >
              إلغاء
            </button>
          </div>
        </div>
      ) : (
        <button
          onClick={() => setInputMode(true)}
          className="w-full bg-gray-800 text-white py-3 px-4 rounded-xl font-bold text-lg"
        >
          اكتب رقم الصفحة
        </button>
      )}
      <div className="w-full flex gap-2">
        <button
          onClick={() => {
            setSelectedAyahInfo(null); // تصفير التظليل عند الدخول من الصفحات
            goToView('pageReader');
          }}
          className="flex-1 bg-gray-800 text-white py-3 px-4 rounded-xl font-bold text-lg"
        >
          اذهب إلى الصفحة {centerPage}
        </button>
        <button
          onClick={handleSave}
          className="flex-1 bg-gray-800 text-white py-3 px-4 rounded-xl font-bold text-lg"
        >
          احفظ رقم الصفحة
        </button>
      </div>
      <div className="w-full flex gap-2">
        <button
          onClick={() => {
            if (!longPressTriggered) handleGoToSaved();
            setLongPressTriggered(false);
          }}
          onPointerDown={e => {
            if (e.pointerType === 'touch' || e.pointerType === 'mouse') {
              const timeout = setTimeout(() => {
                if (savedPage) setSelectedPageNumber(savedPage);
                setLongPressTriggered(true);
              }, 500);
              const up = () => {
                clearTimeout(timeout);
                window.removeEventListener('pointerup', up);
                window.removeEventListener('pointercancel', up);
              };
              window.addEventListener('pointerup', up);
              window.addEventListener('pointercancel', up);
            }
          }}
          className="flex-1 bg-gray-800 text-white py-3 px-4 rounded-xl font-bold text-lg"
        >
          {savedPage || '—'}
          <div className="text-xs text-gray-400 mt-1">الصفحة المحفوظة</div>
        </button>
        <button
          onClick={() => setShowInfo(v => !v)}
          className="w-7 h-7 flex items-center justify-center rounded-full bg-gray-600 text-yellow-200 text-base font-bold ml-2 transition-all"
          title="معلومات"
        >
          i
        </button>
      </div>
      {showInfo && (
        <div className="w-full bg-gray-900 text-gray-200 rounded-xl p-4 text-sm mt-2 shadow border border-gray-700 animate-fade-in">
          يمكنك حفظ رقم الصفحة التي ترغب في الرجوع إليها لاحقًا. اضغط بشكل مطول على الزر (الصفحة المحفوظة) للوصول إلى رقم الصفحة في قائمة الأرقام. أو اضغط مرة للدخول إلى الصفحة في المصحف.
        </div>
      )}
    </div>
  );
};

const Index = () => {
  const [activeTab, setActiveTab] = useState<'suras' | 'ajza' | 'pages'>('suras');
  const [currentView, setCurrentView] = useState<'home' | 'reader' | 'settings' | 'settingsTest' | 'khatma' | 'pageReader' | 'pagesList' | 'bookmarks' | 'duaKhatm' | 'waqfSigns' | 'search'>('home');
  const [viewHistory, setViewHistory] = useState<string[]>([]);
  const [isSettingsModalOpen, setIsSettingsModalOpen] = useState(false);
  const [selectedSuraId, setSelectedSuraId] = useState<number | null>(null);
  const [selectedPageNumber, setSelectedPageNumber] = useState<number | null>(null);
  const [savedSuraId, setSavedSuraId] = useState(1);
  const [suraInput, setSuraInput] = useState('1');

  const [lastReadAyaState, setLastReadAyaState] = useState<{ surahName: string; ayaNumber: number; suraId: number; timestamp: string } | null>(null);

  // استخدام hook الإعدادات الجديد
  const { settings: quranSettings } = useQuranSettings();

  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [showAudioDownload, setShowAudioDownload] = useState(false);

  const [selectedAyahInfo, setSelectedAyahInfo] = useState<{ page: number, surah: number, ayah: number } | null>(null);
  const [khatmaNavigationContext, setKhatmaNavigationContext] = useState<{
    fromWird?: boolean;
    wirdDay?: number;
    khatmaDuration?: number;
  }>({});

  const [showPlaySettings, setShowPlaySettings] = useState(false);

  // مراقبة تغيير showPlaySettings
  useEffect(() => {
    console.log('📊 Index.tsx - showPlaySettings changed to:', showPlaySettings);
  }, [showPlaySettings]);

  useEffect(() => {
    const savedLastReadAya = localStorage.getItem('lastReadAya');
    if (savedLastReadAya) {
      setLastReadAyaState(JSON.parse(savedLastReadAya));
    }

    // Always ensure we start with home view
    setCurrentView('home');
  }, []);

  const saveLastReadSuraAndAyaToLocalStorage = (suraId: number, ayaId: number) => {
    const sura = quranData.find((s: any) => s.id === suraId);
    const newLastReadAya = {
      surahName: sura?.name || 'غير معروف',
      ayaNumber: ayaId,
      suraId: suraId,
      timestamp: new Date().toISOString(),
    };
    localStorage.setItem('lastReadAya', JSON.stringify(newLastReadAya));
    setLastReadAyaState(newLastReadAya);
  };

  const handleSuraClick = (suraId: number) => {
    setSelectedAyahInfo(null); // منع التظليل عند الدخول من السور
    // البحث عن الصفحة الأولى للسورة
    const sura = suras.find(s => s.id === suraId);
    if (sura && sura.page) {
      setSelectedPageNumber(sura.page);
      goToView('pageReader');
    }
  };

  const handleJuzClick = (juzId: number) => {
    setSelectedAyahInfo(null); // منع التظليل عند الدخول من الأجزاء
    console.log('Juz clicked:', juzId);
    const juz = ajzaa.find(j => j.id === juzId);
    if (juz && juz.page) {
      // التأكد من أن الصفحة صحيحة (بداية الجزء وليس قبلها)
      const correctPage = juz.page;
      console.log(`Opening Juz ${juzId} at page ${correctPage}`);
      setSelectedPageNumber(correctPage);
      goToView('pageReader');
    }
  };

  const handleContinueFromAya = (suraId: number, ayaId: number, page?: number) => {
    // البحث عن السورة عبر id فقط
    const sura = suras.find(s => s.id === suraId);
    const pageNum = page || sura?.page;
    if (sura && pageNum) {
      setSelectedPageNumber(pageNum);
      setSelectedAyahInfo({ page: pageNum, surah: suraId, ayah: ayaId });
      goToView('pageReader');
      saveLastReadSuraAndAyaToLocalStorage(suraId, ayaId);
    }
  };

  const handlePageClick = (pageNumber: number) => {
    setSelectedAyahInfo(null); // منع التظليل عند الدخول من الصفحات
    setSelectedPageNumber(pageNumber);
    goToView('pageReader');
  };

  const handleKhatmaPageClick = (pageNumber: number, context?: any) => {
    if (context) {
      setKhatmaNavigationContext(context);
    }
    setSelectedPageNumber(pageNumber);
    goToView('pageReader');
  };

  const handleKhatmaAyahClick = (suraId: number, ayaId: number, page?: number, context?: any) => {
    if (context) {
      setKhatmaNavigationContext(context);
    }
    handleContinueFromAya(suraId, ayaId, page);
  };

  const handleShowPagesList = () => {
    setCurrentView('pagesList');
  };

  // دالة للانتقال مع حفظ التاريخ
  const goToView = (view: string) => {
    setViewHistory(prev => [...prev, currentView]);
    setCurrentView(view as any);
  };

  // دالة الرجوع
  const goBack = () => {
    // إذا كان المستخدم يعود من المصحف وكان قد جاء من ورد، عد إلى الورد المحدد
    if (currentView === 'pageReader' && khatmaNavigationContext.fromWird) {
      setCurrentView('khatma');
      // مسح السياق بعد الاستخدام
      setKhatmaNavigationContext({});
      return;
    }

    setViewHistory(prev => {
      if (prev.length === 0) {
        setCurrentView('home');
        return prev;
      }
      const lastView = prev[prev.length - 1] as any;
      setCurrentView(lastView);
      return prev.slice(0, -1);
    });
  };

  const webAudio = useWebAudioPlayer();
  const audioPlayer = useAudioPlayer();

  if (currentView === 'reader' && selectedSuraId !== null) {
    return (
      <QuranReader
        initialSuraId={selectedSuraId}
        onBack={goBack}
        onShowSettings={() => setCurrentView('settings')}
        onAyaRead={saveLastReadSuraAndAyaToLocalStorage}
        quranSettings={quranSettings}
      />
    );
  }

  if (currentView === 'pageReader' && selectedPageNumber !== null) {
    return (
      <>
        <PageReader
          initialPage={selectedPageNumber}
          targetAyahInfo={selectedAyahInfo}
          onClearTargetAyahInfo={() => setSelectedAyahInfo(null)}
          onBack={goBack}
          onShowSettings={() => setIsSettingsModalOpen(true)}
          showPlaySettings={showPlaySettings}
          setShowPlaySettings={setShowPlaySettings}
          webAudioCurrentAyah={webAudio.state.currentAyah}
          audioPlayer={audioPlayer}
        />
        {isSettingsModalOpen && (
          <div className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center">
            <div className="w-full max-w-lg max-h-[90vh] overflow-y-auto rounded-2xl">
              <UnifiedQuranSettings
                onBack={() => setIsSettingsModalOpen(false)}
                isModal={true}
                onClose={() => setIsSettingsModalOpen(false)}
              />
            </div>
          </div>
        )}
      </>
    );
  }

  if (currentView === 'pagesList') {
    return (
      <PagesList
        onPageSelect={(pageNumber) => {
          setSelectedPageNumber(pageNumber);
          goToView('pageReader');
        }}
        currentPage={selectedPageNumber || 1}
        onBack={goBack}
      />
    );
  }

  if (currentView === 'settings') {
    return <UnifiedQuranSettings
      onBack={goBack}
      isModal={false}
    />;
  }

  if (currentView === 'settingsTest') {
    return <SettingsTestPage
      onBack={goBack}
      onGoToReader={() => {
        setSelectedPageNumber(1);
        goToView('pageReader');
      }}
    />;
  }

  if (currentView === 'khatma') {
    return <KhatmaTracker
      onBack={goBack}
      onGoToPage={(pageNumber: number, context?: any) => {
        if (context) {
          setKhatmaNavigationContext(context);
        }
        handlePageClick(pageNumber);
      }}
      onGoToAyah={(suraId: number, ayaId: number, page?: number, context?: any) => {
        if (context) {
          setKhatmaNavigationContext(context);
        }
        handleContinueFromAya(suraId, ayaId, page);
      }}
    />;
  }

  if (currentView === 'bookmarks') {
    return (
      <BookmarksPage
        onBack={goBack}
        onGoToPage={(pageNumber) => {
          setSelectedPageNumber(pageNumber);
          goToView('pageReader');
        }}
        onGoToAyah={handleContinueFromAya}
      />
    );
  }

  if (currentView === 'duaKhatm') {
    return <DuaKhatmPage onBack={goBack} />;
  }

  if (currentView === 'waqfSigns') {
    return <WaqfSignsPage onBack={goBack} />;
  }

  if (currentView === 'search') {
    return <SearchPage
      onBack={goBack}
      onResultClick={(type, data) => {
        if (type === 'surah') {
          // انتقل لأول صفحة في السورة
          const page = parseInt(data.pages);
          setSelectedPageNumber(page);
          setSelectedAyahInfo(null);
          goToView('pageReader');
        } else if (type === 'ayah') {
          // انتقل لصفحة الآية
          const page = data.page;
          setSelectedPageNumber(page);
          setSelectedAyahInfo({ page, surah: data.surah.number, ayah: data.numberInSurah });
          goToView('pageReader');
        }
      }}
    />;
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <QuranHeader
        title="المصحف"
        onSearch={() => setCurrentView('search')}
        onSettings={() => setCurrentView('settings')}
      />
      {/* Quran Search Modal */}
      <CommandDialog open={isSearchOpen} onOpenChange={setIsSearchOpen}>
        <QuranSearch />
      </CommandDialog>
      {/* Bookmarks Section - Improved Design */}
      <div className="bg-gray-900 p-4">
        <div
          onClick={() => setCurrentView('bookmarks')}
          className="bg-gray-800 rounded-lg p-4 cursor-pointer hover:bg-gray-700 transition-colors flex items-center justify-between"
        >
          <BookmarkIcon size={24} className="text-blue-400" />
          <div className="text-right">
            <h3 className="text-white font-bold text-lg">الفواصل المحفوظة</h3>
            <p className="text-gray-400 text-sm">إدارة العلامات والفواصل</p>
          </div>
        </div>
      </div>
      
      {/* تم حذف مكون LastRead بناءً على طلب المستخدم */}

      {/* Khatma Section */}
      <div className="bg-gray-900 p-4">
        <div
          onClick={() => setCurrentView('khatma')}
          className="bg-gray-800 rounded-lg p-4 cursor-pointer hover:bg-gray-700 transition-colors flex items-center justify-between"
        >
          <svg width="40" height="40" viewBox="0 0 40 40" fill="none" className="text-green-400">
            <path d="M20 5 L30 15 L30 25 L20 35 L10 25 L10 15 Z" stroke="currentColor" strokeWidth="2" fill="none"/>
            <path d="M15 17 L25 17 M15 20 L25 20 M15 23 L25 23" stroke="currentColor" strokeWidth="1"/>
          </svg>
          <div className="text-right">
            <h3 className="text-white font-bold">ختمة</h3>
          </div>
        </div>
      </div>

      {/* Audio Download Section */}
      <div className="bg-gray-900 p-4">
        <div
          onClick={() => setShowAudioDownload(true)}
          className="bg-gray-800 rounded-lg p-4 cursor-pointer hover:bg-gray-700 transition-colors flex items-center justify-between"
        >
          <Download size={40} className="text-yellow-400" />
          <div className="text-right">
            <h3 className="text-white font-bold text-lg">تحميل الصوتيات</h3>
            <p className="text-gray-400 text-sm">تحميل القرآن بصوت الشيوخ</p>
          </div>
        </div>
      </div>
      
      <QuranTabs activeTab={activeTab} onTabChange={setActiveTab} />
      <div className="p-4 space-y-3 pb-20">
        {activeTab === 'suras' && (
          <>
            <div className="grid grid-cols-2 gap-3">
              {suras.map((sura, index) => (
                <div key={sura.id} className={`pb-3 ${index < suras.length - 1 ? 'border-b border-gray-700 mb-3' : ''}`}>
                  <SuraCard
                    id={sura.id}
                    name={sura.name}
                    arabicName={sura.arabicName}
                    verses={sura.verses}
                    type={sura.type}
                    page={sura.page}
                    onClick={() => handleSuraClick(sura.id)}
                  />
                </div>
              ))}
            </div>
            {/* أزرار دعاء ختم القرآن وعلامات الوقف والوصل بعد السور فقط */}
            <div className="flex flex-col gap-4 pt-8">
              <button
                onClick={() => setCurrentView('duaKhatm')}
                className="w-full flex items-center justify-between bg-gradient-to-l from-green-700 to-green-500 hover:from-green-800 hover:to-green-600 text-white py-4 px-5 rounded-2xl font-bold text-lg shadow-lg transition-all duration-200 border-2 border-green-800"
              >
                <span className="flex items-center gap-2">
                  <svg width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-white"><path d="M12 20v-6"/><path d="M6 20v-4"/><path d="M18 20v-4"/><path d="M2 20h20"/><path d="M4 4h16v4H4z"/><path d="M4 8v4h16V8"/></svg>
                  دعاء ختم القرآن
                </span>
                <span className="text-green-200 text-base">→</span>
              </button>
              <button
                onClick={() => setCurrentView('waqfSigns')}
                className="w-full flex items-center justify-between bg-gradient-to-l from-blue-700 to-blue-500 hover:from-blue-800 hover:to-blue-600 text-white py-4 px-5 rounded-2xl font-bold text-lg shadow-lg transition-all duration-200 border-2 border-blue-800"
              >
                <span className="flex items-center gap-2">
                  <svg width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-white"><circle cx="12" cy="12" r="10"/><path d="M12 8v4"/><path d="M12 16h.01"/></svg>
                  علامات الوقف والوصل
                </span>
                <span className="text-blue-200 text-base">→</span>
              </button>
            </div>
          </>
        )}
        {activeTab === 'ajza' && (
          <div className="grid grid-cols-2 gap-3">
            {ajzaa.map((juz) => (
              <JuzCard
                key={juz.id}
                id={juz.id}
                name={juz.name}
                page={juz.page}
                onClick={() => handleJuzClick(juz.id)}
              />
            ))}
          </div>
        )}
        {/* تبويب الصفحات */}
        {activeTab === 'pages' && (
          <PagesModernTab
            selectedPageNumber={selectedPageNumber}
            setSelectedPageNumber={setSelectedPageNumber}
            goToView={goToView}
            setSelectedAyahInfo={setSelectedAyahInfo}
          />
        )}
      </div>

      {/* Audio Download Manager Modal */}
      <AudioDownloadManager
        open={showAudioDownload}
        onClose={() => setShowAudioDownload(false)}
      />
    </div>
  );
};

export default Index;
