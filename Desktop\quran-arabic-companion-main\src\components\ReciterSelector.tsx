import React, { useState } from 'react';
import { Search, ChevronDown, ChevronUp } from 'lucide-react';
import { RECITATION_TYPES } from '../hooks/useAudioPlayer';

interface ReciterSelectorProps {
  selectedReciterId: string;
  onReciterSelect: (reciter: any) => void;
  onClose?: () => void;
}

const ReciterSelector: React.FC<ReciterSelectorProps> = ({ selectedReciterId, onReciterSelect, onClose }) => {
  const [search, setSearch] = useState('');
  const [expandedRecitations, setExpandedRecitations] = useState<string[]>(['hafs']); // حفص مفتوح افتراضياً

  // دالة لتبديل حالة الرواية (مفتوحة/مغلقة)
  const toggleRecitation = (recitationId: string) => {
    setExpandedRecitations(prev =>
      prev.includes(recitationId)
        ? prev.filter(id => id !== recitationId)
        : [...prev, recitationId]
    );
  };

  // فلترة القراء حسب البحث
  const getFilteredReciters = (reciters: any[]) => {
    if (!search) return reciters;
    return reciters.filter(r => r.name.includes(search));
  };
  return (
    <div className="w-full max-w-xs sm:max-w-sm lg:max-w-md mx-auto p-2 sm:p-4 bg-white dark:bg-gray-900 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 flex flex-col">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-bold text-yellow-500">اختر القارئ</h3>
      </div>

      {/* شريط البحث */}
      <div className="relative mb-4">
        <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">
          <Search size={18} />
        </span>
        <input
          type="text"
          className="w-full p-2 pl-10 pr-3 border border-gray-300 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-400 bg-gray-50 dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
          placeholder="ابحث عن القارئ..."
          value={search}
          onChange={e => setSearch(e.target.value)}
        />
      </div>

      {/* قائمة الروايات والقراء */}
      <div className="space-y-3 min-h-[30vh] max-h-[50vh] sm:max-h-[60vh] lg:max-h-[70vh] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-700 scrollbar-track-transparent flex-1">
        {Object.values(RECITATION_TYPES).map(recitation => {
          const filteredReciters = getFilteredReciters(recitation.reciters);
          const isExpanded = expandedRecitations.includes(recitation.id);

          // إخفاء الرواية إذا لم تحتوي على قراء مطابقين للبحث
          if (search && filteredReciters.length === 0) return null;

          return (
            <div key={recitation.id} className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
              {/* عنوان الرواية */}
              <button
                onClick={() => toggleRecitation(recitation.id)}
                className="w-full p-3 bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors flex items-center justify-between text-right"
              >
                <div className="flex-1">
                  <h4 className="font-bold text-gray-900 dark:text-white">{recitation.name}</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{recitation.description}</p>
                  <span className="text-xs text-gray-500 dark:text-gray-500">
                    {filteredReciters.length} قارئ متاح
                  </span>
                </div>
                <div className="mr-3 text-gray-500">
                  {isExpanded ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
                </div>
              </button>

              {/* قائمة القراء */}
              {isExpanded && (
                <div className="bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700">
                  {filteredReciters.length === 0 ? (
                    <div className="text-center text-gray-400 py-4">لا يوجد قراء مطابقين للبحث</div>
                  ) : (
                    <div className="space-y-1 p-2">
                      {filteredReciters.map(reciter => (
                        <button
                          key={reciter.id}
                          className={`w-full text-right p-3 rounded-lg transition font-medium border border-transparent hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-900 dark:text-gray-100 ${
                            selectedReciterId === reciter.id
                              ? 'bg-yellow-100 dark:bg-yellow-900 text-yellow-900 dark:text-yellow-200 border-yellow-400 dark:border-yellow-600 shadow'
                              : ''
                          }`}
                          onClick={() => onReciterSelect({
                            ...reciter,
                            recitation: recitation.id,
                            recitationName: recitation.name
                          })}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex-1">
                              <div className="font-medium">{reciter.name}</div>
                              <div className="text-xs text-gray-500 dark:text-gray-400 mt-1 flex items-center gap-2">
                                <span>🎵 {reciter.style}</span>
                                <span>🌍 {reciter.country}</span>
                                <span>📊 {reciter.bitrate}</span>
                              </div>
                            </div>
                          </div>
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>
          );
        })}

        {/* رسالة عدم وجود نتائج */}
        {search && Object.values(RECITATION_TYPES).every(recitation =>
          getFilteredReciters(recitation.reciters).length === 0
        ) && (
          <div className="text-center text-gray-400 py-8">
            <Search size={48} className="mx-auto mb-4 opacity-50" />
            <p>لا يوجد قراء مطابقين للبحث "{search}"</p>
          </div>
        )}
      </div>

      <button
        className="w-full py-3 rounded-xl bg-gray-200 dark:bg-gray-800 text-gray-800 dark:text-gray-100 font-medium hover:bg-gray-300 dark:hover:bg-gray-700 transition-colors mt-4"
        onClick={onClose}
      >
        إغلاق
      </button>
    </div>
  );
};

export default ReciterSelector; 