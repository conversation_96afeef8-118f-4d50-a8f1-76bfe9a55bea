import React, { useState } from 'react';
import { useBookmarks, AyahBookmark, PageBookmark, Bookmark } from '../hooks/useBookmarks';
import surahsInfo from '../data/surah.json';
import ayahData from '../data/ayahobject.json';
import { useQuranSettings } from '../hooks/useQuranSettings';
import { Bookmark as BookmarkIcon, Trash2, Edit, ChevronRight } from 'lucide-react';

interface BookmarksPageProps {
  onBack: () => void;
  onGoToPage: (page: number) => void;
  onGoToAyah?: (surah: number, ayah: number, page: number) => void;
}

const BookmarksPage: React.FC<BookmarksPageProps> = ({ onBack, onGoToPage, onGoToAyah }) => {
  const { bookmarks, removeBookmark } = useBookmarks();
  const { settings } = useQuranSettings();
  const [sortBy, setSortBy] = useState<'page' | 'date'>('date'); // 'page' or 'date'

  const isDarkMode = settings.displayMode === 'dark' ||
    (settings.displayMode === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches);

  const convertToArabicNumbers = (num: number) => {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return num.toString().split('').map(digit =>
      /\d/.test(digit) ? arabicNumbers[parseInt(digit)] : digit
    ).join('');
  };

  const getAyahText = (surahNumber: number, ayahNumber: number) => {
    const ayah = (ayahData as any[]).find(a => a.surah.number === surahNumber && a.numberInSurah === ayahNumber);
    return ayah ? ayah.uthmaniText : 'نص الآية غير متوفر';
  };

  const getSurahName = (surahNumber: number) => {
    const surah = surahsInfo.find(s => parseInt(s.index) === surahNumber);
    return surah ? `سورة ${surah.titleAr}` : '';
  };

  const getBookmarkTimestamp = (bookmark: any) => bookmark.timestamp ? new Date(bookmark.timestamp) : new Date();

  const getTimeAgo = (bookmark: any) => {
    const now = new Date().getTime();
    const ts = getBookmarkTimestamp(bookmark).getTime();
    const diff = now - ts;
    const hours = Math.floor(diff / (1000 * 60 * 60));
    if (hours === 0) return 'الآن';
    if (hours < 24) return `قبل ${convertToArabicNumbers(hours)} ساعة`;
    const days = Math.floor(hours / 24);
    return `قبل ${convertToArabicNumbers(days)} يوم`;
  };

  const sortedBookmarks = [...bookmarks].sort((a, b) => {
    if (sortBy === 'page') {
      const pageA = a.type === 'page' ? a.page : ((ayahData as any[]).find(ayah => ayah.surah && ayah.surah.number === a.surah && ayah.numberInSurah === a.ayah)?.page || 0);
      const pageB = b.type === 'page' ? b.page : ((ayahData as any[]).find(ayah => ayah.surah && ayah.surah.number === b.surah && ayah.numberInSurah === b.ayah)?.page || 0);
      return pageA - pageB;
    } else {
      // Sort by timestamp (الأحدث أولاً)
      return getBookmarkTimestamp(b).getTime() - getBookmarkTimestamp(a).getTime();
    }
  });

  const handleDeleteAll = () => {
    if (window.confirm('هل أنت متأكد أنك تريد حذف جميع الفواصل؟')) {
      // Create a copy of bookmarks to iterate over, as removeBookmark modifies the state
      [...bookmarks].forEach(bookmark => removeBookmark(bookmark));
    }
  };

  return (
    <div className={`min-h-screen ${isDarkMode ? 'bg-gray-900 text-white' : 'bg-white text-black'}`}>
      {/* Header */}
      <div className="sticky top-0 z-20 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm shadow-sm p-4 flex items-center justify-between">
        <button onClick={onBack} className="text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 p-2 rounded-full">
          <ChevronRight size={20} /> {/* Back icon */}
        </button>
        <h1 className="text-lg font-bold">الفواصل المحفوظة</h1>
      </div>

      {/* Sort options */}
      <div className="p-4 flex justify-center gap-4 bg-gray-100 dark:bg-gray-800">
        <button
          onClick={() => setSortBy('page')}
          className={`px-4 py-2 rounded-full text-sm font-medium ${
            sortBy === 'page'
              ? 'bg-blue-500 text-white'
              : 'bg-gray-200 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
          }`}
        >
          ترتيب حسب رقم الصفحة
        </button>
        <button
          onClick={() => setSortBy('date')}
          className={`px-4 py-2 rounded-full text-sm font-medium ${
            sortBy === 'date'
              ? 'bg-blue-500 text-white'
              : 'bg-gray-200 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
          }`}
        >
          ترتيب حسب التاريخ
        </button>
      </div>

      {/* Bookmarks List */}
      <div className="p-4 space-y-4 pb-28">
        {sortedBookmarks.length === 0 ? (
          <p className="text-center text-gray-500 dark:text-gray-400 mt-8">لا توجد فواصل محفوظة.</p>
        ) : (
          sortedBookmarks.map((bookmark, index) => {
            const isAyah = bookmark.type === 'ayah';
            let ayahObj: any = undefined;
            let pageNum: number;
            let surahName = '';
            let ayahNum: number | undefined = undefined;
            let ayahText = '';
            if (isAyah) {
              ayahObj = (ayahData as any[]).find(ayah => ayah.surah && ayah.surah.number === (bookmark as AyahBookmark).surah && ayah.numberInSurah === (bookmark as AyahBookmark).ayah);
              if (!ayahObj) {
                // إذا لم توجد الآية، تجاهل الفاصل أو اعرض رسالة مناسبة
                return null;
              }
              pageNum = ayahObj.page;
              surahName = getSurahName((bookmark as AyahBookmark).surah);
              ayahNum = (bookmark as AyahBookmark).ayah;
              ayahText = ayahObj.uthmaniText;
            } else {
              pageNum = (bookmark as PageBookmark).page;
            }
            return (
              <div
                key={index}
                className={`rounded-xl shadow flex flex-col gap-2 p-4 relative border ${isDarkMode ? 'bg-gray-900 border-gray-800' : 'bg-white border-gray-200'}`}
                onClick={() => {
                  if (isAyah && onGoToAyah && ayahObj) {
                    onGoToAyah(ayahObj.surah.number, ayahObj.numberInSurah, ayahObj.page);
                  } else if (onGoToPage) {
                    onGoToPage(pageNum);
                  }
                }}
                style={{ cursor: 'pointer' }}
              >
                {/* نوع الفاصل */}
                <div className="flex items-center gap-2 mb-1">
                  <span className={`inline-block w-2 h-2 rounded-full`} style={{ background: isAyah ? (bookmark as AyahBookmark).color : (isDarkMode ? '#fff700' : '#facc15') }}></span>
                  <span className={`text-xs font-bold ${isAyah ? 'text-blue-500 dark:text-blue-400' : 'text-yellow-600 dark:text-yellow-400'}`}>{isAyah ? 'آية' : 'صفحة'}</span>
                  <span className="text-xs text-gray-400">{getTimeAgo(bookmark)}</span>
                </div>
                {/* التفاصيل */}
                <div className="flex flex-col gap-1">
                  {isAyah ? (
                    <>
                      <div className="flex items-center justify-between">
                        <span className="font-semibold text-right text-base truncate max-w-[60%]">{surahName} - الآية {convertToArabicNumbers(ayahNum!)}</span>
                        <span className="text-xs text-gray-500 dark:text-gray-400">صفحة {convertToArabicNumbers(pageNum)}</span>
                      </div>
                      <div className="text-sm text-gray-700 dark:text-gray-300 text-right truncate max-w-full" style={{ direction: 'rtl' }}>{ayahText}</div>
                    </>
                  ) : (
                    <div className="flex items-center justify-between">
                      <span className="font-semibold text-right text-base">صفحة {convertToArabicNumbers((bookmark as PageBookmark).page)}</span>
                    </div>
                  )}
                </div>
                {/* زر حذف */}
                <button
                  onClick={e => {
                    e.stopPropagation();
                    if (bookmark.type === 'ayah') {
                      // احذف فقط الفاصل بهذا اللون
                      removeBookmark({ ...bookmark });
                    } else {
                      removeBookmark(bookmark);
                    }
                  }}
                  className="absolute left-3 top-3 text-red-500 hover:bg-red-100 dark:hover:bg-red-900 p-1 rounded-full"
                  title="حذف الفاصل"
                >
                  <Trash2 size={18} />
                </button>
              </div>
            );
          })
        )}
      </div>
    </div>
  );
};

export default BookmarksPage;
