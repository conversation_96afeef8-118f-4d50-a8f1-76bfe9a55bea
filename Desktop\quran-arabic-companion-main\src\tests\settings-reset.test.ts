/**
 * اختبارات إعادة تعيين الإعدادات
 * Settings Reset Tests
 */

import { QuranSettings } from '../hooks/useQuranSettings';

const STORAGE_KEY = 'quran-settings';

/**
 * الإعدادات الافتراضية المتوقعة
 */
const DEFAULT_SETTINGS: QuranSettings = {
  mushafType: 'connected',
  colorTheme: 'gray',
  displayMode: 'auto',
  fontSize: 20,
  numberFormat: 'arabic',
  textAlignment: 'right',
  showSurahNames: true,
  showJuzInfo: true
};

/**
 * اختبار إعادة تعيين الإعدادات للقيم الافتراضية
 */
export const testSettingsReset = (): boolean => {
  console.log('🧪 اختبار إعادة تعيين الإعدادات...');
  
  try {
    // حفظ الإعدادات الحالية
    const originalSettings = localStorage.getItem(STORAGE_KEY);
    
    // إنشاء إعدادات مخصصة مختلفة عن الافتراضية
    const customSettings: QuranSettings = {
      mushafType: 'separated',
      colorTheme: 'blue',
      displayMode: 'dark',
      fontSize: 28,
      numberFormat: 'english',
      textAlignment: 'justified',
      showSurahNames: false,
      showJuzInfo: false
    };
    
    // حفظ الإعدادات المخصصة
    localStorage.setItem(STORAGE_KEY, JSON.stringify(customSettings));
    
    // التحقق من حفظ الإعدادات المخصصة
    const savedCustom = localStorage.getItem(STORAGE_KEY);
    if (!savedCustom) {
      console.log('❌ فشل في حفظ الإعدادات المخصصة');
      return false;
    }
    
    const parsedCustom = JSON.parse(savedCustom);
    console.log('✅ تم حفظ الإعدادات المخصصة بنجاح');
    
    // محاكاة إعادة تعيين الإعدادات
    localStorage.setItem(STORAGE_KEY, JSON.stringify(DEFAULT_SETTINGS));
    
    // التحقق من إعادة التعيين
    const resetSettings = localStorage.getItem(STORAGE_KEY);
    if (!resetSettings) {
      console.log('❌ فشل في إعادة تعيين الإعدادات');
      return false;
    }
    
    const parsedReset = JSON.parse(resetSettings);
    
    // مقارنة كل إعداد مع القيم الافتراضية
    const settingsKeys = Object.keys(DEFAULT_SETTINGS) as (keyof QuranSettings)[];
    let matchedSettings = 0;
    
    for (const key of settingsKeys) {
      if (parsedReset[key] === DEFAULT_SETTINGS[key]) {
        console.log(`✅ ${key}: ${parsedReset[key]} (صحيح)`);
        matchedSettings++;
      } else {
        console.log(`❌ ${key}: ${parsedReset[key]} (متوقع: ${DEFAULT_SETTINGS[key]})`);
      }
    }
    
    // إعادة الإعدادات الأصلية
    if (originalSettings) {
      localStorage.setItem(STORAGE_KEY, originalSettings);
    } else {
      localStorage.removeItem(STORAGE_KEY);
    }
    
    const success = matchedSettings === settingsKeys.length;
    
    if (success) {
      console.log(`🎉 نجح اختبار إعادة التعيين! (${matchedSettings}/${settingsKeys.length})`);
    } else {
      console.log(`⚠️ فشل في ${settingsKeys.length - matchedSettings} من ${settingsKeys.length} إعدادات`);
    }
    
    return success;
    
  } catch (error) {
    console.error('خطأ في اختبار إعادة التعيين:', error);
    return false;
  }
};

/**
 * اختبار إعادة تعيين إعدادات محددة
 */
export const testPartialSettingsReset = (): boolean => {
  console.log('🧪 اختبار إعادة تعيين إعدادات محددة...');
  
  try {
    // حفظ الإعدادات الحالية
    const originalSettings = localStorage.getItem(STORAGE_KEY);
    
    // إنشاء إعدادات مختلطة
    const mixedSettings: QuranSettings = {
      ...DEFAULT_SETTINGS,
      fontSize: 32, // مختلف عن الافتراضي
      colorTheme: 'red', // مختلف عن الافتراضي
      displayMode: 'dark' // مختلف عن الافتراضي
    };
    
    localStorage.setItem(STORAGE_KEY, JSON.stringify(mixedSettings));
    
    // إعادة تعيين إعدادات محددة فقط
    const partialReset: Partial<QuranSettings> = {
      fontSize: DEFAULT_SETTINGS.fontSize,
      colorTheme: DEFAULT_SETTINGS.colorTheme
      // ترك displayMode كما هو
    };
    
    const updatedSettings = {
      ...mixedSettings,
      ...partialReset
    };
    
    localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedSettings));
    
    // التحقق من النتيجة
    const result = localStorage.getItem(STORAGE_KEY);
    if (!result) return false;
    
    const parsed = JSON.parse(result);
    
    const tests = [
      { key: 'fontSize', expected: DEFAULT_SETTINGS.fontSize, actual: parsed.fontSize },
      { key: 'colorTheme', expected: DEFAULT_SETTINGS.colorTheme, actual: parsed.colorTheme },
      { key: 'displayMode', expected: 'dark', actual: parsed.displayMode } // يجب أن يبقى كما هو
    ];
    
    let passedTests = 0;
    
    for (const test of tests) {
      if (test.actual === test.expected) {
        console.log(`✅ ${test.key}: ${test.actual} (صحيح)`);
        passedTests++;
      } else {
        console.log(`❌ ${test.key}: ${test.actual} (متوقع: ${test.expected})`);
      }
    }
    
    // إعادة الإعدادات الأصلية
    if (originalSettings) {
      localStorage.setItem(STORAGE_KEY, originalSettings);
    } else {
      localStorage.removeItem(STORAGE_KEY);
    }
    
    const success = passedTests === tests.length;
    
    if (success) {
      console.log(`🎉 نجح اختبار إعادة التعيين الجزئي! (${passedTests}/${tests.length})`);
    } else {
      console.log(`⚠️ فشل في ${tests.length - passedTests} من ${tests.length} اختبارات`);
    }
    
    return success;
    
  } catch (error) {
    console.error('خطأ في اختبار إعادة التعيين الجزئي:', error);
    return false;
  }
};

/**
 * اختبار إعادة تعيين الإعدادات مع بيانات تالفة
 */
export const testResetWithCorruptedData = (): boolean => {
  console.log('🧪 اختبار إعادة التعيين مع بيانات تالفة...');
  
  try {
    // حفظ الإعدادات الحالية
    const originalSettings = localStorage.getItem(STORAGE_KEY);
    
    // إنشاء بيانات تالفة
    const corruptedData = '{"invalid": json, syntax}';
    localStorage.setItem(STORAGE_KEY, corruptedData);
    
    // محاولة قراءة البيانات التالفة
    try {
      const data = localStorage.getItem(STORAGE_KEY);
      JSON.parse(data || '{}');
      console.log('❌ لم يتم اكتشاف البيانات التالفة');
      return false;
    } catch (parseError) {
      console.log('✅ تم اكتشاف البيانات التالفة بنجاح');
    }
    
    // إعادة تعيين الإعدادات للقيم الافتراضية
    localStorage.setItem(STORAGE_KEY, JSON.stringify(DEFAULT_SETTINGS));
    
    // التحقق من الإعدادات الجديدة
    const resetData = localStorage.getItem(STORAGE_KEY);
    if (!resetData) {
      console.log('❌ فشل في إعادة تعيين الإعدادات بعد البيانات التالفة');
      return false;
    }
    
    const parsed = JSON.parse(resetData);
    
    // التحقق من صحة البيانات الجديدة
    const isValid = Object.keys(DEFAULT_SETTINGS).every(key => 
      key in parsed && parsed[key] === DEFAULT_SETTINGS[key as keyof QuranSettings]
    );
    
    // إعادة الإعدادات الأصلية
    if (originalSettings) {
      localStorage.setItem(STORAGE_KEY, originalSettings);
    } else {
      localStorage.removeItem(STORAGE_KEY);
    }
    
    if (isValid) {
      console.log('🎉 نجح اختبار إعادة التعيين مع البيانات التالفة!');
    } else {
      console.log('❌ فشل في إعادة التعيين مع البيانات التالفة');
    }
    
    return isValid;
    
  } catch (error) {
    console.error('خطأ في اختبار البيانات التالفة:', error);
    return false;
  }
};

/**
 * اختبار إعادة تعيين الإعدادات مع localStorage فارغ
 */
export const testResetWithEmptyStorage = (): boolean => {
  console.log('🧪 اختبار إعادة التعيين مع localStorage فارغ...');
  
  try {
    // حفظ الإعدادات الحالية
    const originalSettings = localStorage.getItem(STORAGE_KEY);
    
    // مسح localStorage
    localStorage.removeItem(STORAGE_KEY);
    
    // التحقق من عدم وجود إعدادات
    const emptyCheck = localStorage.getItem(STORAGE_KEY);
    if (emptyCheck !== null) {
      console.log('❌ فشل في مسح localStorage');
      return false;
    }
    
    console.log('✅ تم مسح localStorage بنجاح');
    
    // إعادة تعيين الإعدادات
    localStorage.setItem(STORAGE_KEY, JSON.stringify(DEFAULT_SETTINGS));
    
    // التحقق من الإعدادات الجديدة
    const newSettings = localStorage.getItem(STORAGE_KEY);
    if (!newSettings) {
      console.log('❌ فشل في إنشاء إعدادات جديدة');
      return false;
    }
    
    const parsed = JSON.parse(newSettings);
    
    // التحقق من صحة الإعدادات
    const isValid = Object.keys(DEFAULT_SETTINGS).every(key => 
      key in parsed && parsed[key] === DEFAULT_SETTINGS[key as keyof QuranSettings]
    );
    
    // إعادة الإعدادات الأصلية
    if (originalSettings) {
      localStorage.setItem(STORAGE_KEY, originalSettings);
    } else {
      localStorage.removeItem(STORAGE_KEY);
    }
    
    if (isValid) {
      console.log('🎉 نجح اختبار إعادة التعيين مع localStorage فارغ!');
    } else {
      console.log('❌ فشل في إعادة التعيين مع localStorage فارغ');
    }
    
    return isValid;
    
  } catch (error) {
    console.error('خطأ في اختبار localStorage فارغ:', error);
    return false;
  }
};

/**
 * تشغيل جميع اختبارات إعادة التعيين
 */
export const runAllResetTests = (): boolean => {
  console.log('🚀 بدء جميع اختبارات إعادة التعيين...');
  
  const tests = [
    { name: 'إعادة التعيين الكاملة', test: testSettingsReset },
    { name: 'إعادة التعيين الجزئية', test: testPartialSettingsReset },
    { name: 'إعادة التعيين مع بيانات تالفة', test: testResetWithCorruptedData },
    { name: 'إعادة التعيين مع localStorage فارغ', test: testResetWithEmptyStorage }
  ];
  
  let passedTests = 0;
  
  for (const test of tests) {
    console.log(`\n--- اختبار ${test.name} ---`);
    if (test.test()) {
      passedTests++;
    }
  }
  
  const success = passedTests === tests.length;
  
  console.log('\n=== نتيجة اختبارات إعادة التعيين ===');
  if (success) {
    console.log(`🎉 نجحت جميع اختبارات إعادة التعيين! (${passedTests}/${tests.length})`);
  } else {
    console.log(`⚠️ فشل في ${tests.length - passedTests} من ${tests.length} اختبارات إعادة التعيين`);
  }
  
  return success;
};
